<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI论文答辩预测器 | 精准预测，智能分析</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        :root {
            /* 主色调 - 更现代的紫蓝配色 */
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --primary-light: #8b5cf6;
            --secondary: #ec4899;
            --accent: #f59e0b;
            --success: #10b981;
            --warning: #f59e0b;
            --error: #ef4444;
            
            /* 中性色 */
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            
            /* 背景渐变 */
            --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --hero-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
            --card-gradient: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            --feature-gradient: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            
            /* 阴影系统 */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            
            /* 过渡动画 */
            --transition-fast: 0.15s ease-out;
            --transition-base: 0.3s ease-out;
            --transition-slow: 0.5s ease-out;
            
            /* 边界半径 */
            --radius-sm: 0.375rem;
            --radius: 0.5rem;
            --radius-md: 0.75rem;
            --radius-lg: 1rem;
            --radius-xl: 1.5rem;
            --radius-2xl: 2rem;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            line-height: 1.6;
            color: var(--gray-800);
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #f1f5f9 100%);
            min-height: 100vh;
            font-feature-settings: "cv11", "ss01";
            font-variant-numeric: tabular-nums;
        }

        /* 全局滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--gray-100);
            border-radius: var(--radius);
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
            border-radius: var(--radius);
            border: 2px solid var(--gray-100);
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 100%);
        }

        /* 顶部导航 - 更精致的毛玻璃效果 */
        .top-nav {
            position: sticky;
            top: 0;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.85);
            backdrop-filter: blur(20px) saturate(180%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 1px 40px rgba(0, 0, 0, 0.05);
            padding: 1rem 0;
            transition: all var(--transition-base);
        }

        .nav-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: flex-start;
            align-items: center;
        }

        .back-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: white;
            border: 2px solid var(--gray-200);
            border-radius: var(--radius-xl);
            color: var(--gray-700);
            text-decoration: none;
            font-weight: 600;
            font-size: 0.95rem;
            transition: all var(--transition-base);
            box-shadow: var(--shadow-sm);
            position: relative;
            overflow: hidden;
        }

        .back-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--hero-gradient);
            transition: left var(--transition-slow);
        }

        .back-btn span {
            position: relative;
            z-index: 2;
        }

        .back-btn i {
            position: relative;
            z-index: 2;
            transition: transform var(--transition-base);
        }

        .back-btn:hover {
            color: white;
            border-color: transparent;
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .back-btn:hover::before {
            left: 0;
        }

        .back-btn:hover i {
            transform: translateX(-3px);
        }


        /* 英雄区域 - 更动感的设计 */
        .hero-section {
            background: var(--hero-gradient);
            color: white;
            padding: 5rem 0 4rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 900;
            margin-bottom: 1.5rem;
            line-height: 1.1;
            background: linear-gradient(135deg, #ffffff 0%, #e0e7ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }

        .hero-subtitle {
            font-size: 1.375rem;
            margin-bottom: 2.5rem;
            opacity: 0.95;
            max-width: 700px;
            margin-left: auto;
            margin-right: auto;
            font-weight: 400;
            line-height: 1.6;
        }

        .trust-indicators {
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
            margin-top: 3rem;
        }

        .trust-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            background: rgba(255, 255, 255, 0.15);
            padding: 1rem 2rem;
            border-radius: var(--radius-xl);
            font-size: 1rem;
            font-weight: 600;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all var(--transition-base);
            cursor: default;
        }

        .trust-item:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .trust-item i {
            font-size: 1.25rem;
        }

        /* 功能特点区域 */
        .features-section {
            padding: 5rem 0;
            background: white;
            position: relative;
        }

        .section-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-title {
            font-size: 3rem;
            font-weight: 800;
            color: var(--gray-900);
            margin-bottom: 1.5rem;
            background: var(--hero-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .section-subtitle {
            font-size: 1.25rem;
            color: var(--gray-600);
            max-width: 600px;
            margin: 0 auto;
            font-weight: 400;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
            gap: 2.5rem;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .feature-card {
            background: white;
            padding: 3rem 2.5rem;
            border-radius: var(--radius-2xl);
            text-align: center;
            border: 1px solid var(--gray-200);
            transition: all var(--transition-slow);
            box-shadow: var(--shadow-sm);
            position: relative;
            overflow: hidden;
            cursor: default;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--hero-gradient);
            transform: scaleX(0);
            transition: transform var(--transition-slow);
        }

        .feature-card::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: radial-gradient(circle, rgba(99, 102, 241, 0.05) 0%, transparent 70%);
            transform: translate(-50%, -50%);
            transition: all var(--transition-slow);
            border-radius: 50%;
        }

        .feature-card:hover::before {
            transform: scaleX(1);
        }

        .feature-card:hover::after {
            width: 400px;
            height: 400px;
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-2xl);
            border-color: var(--primary);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: var(--hero-gradient);
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin: 0 auto 2rem;
            box-shadow: var(--shadow-lg);
            transition: all var(--transition-base);
            position: relative;
            z-index: 2;
        }

        .feature-card:hover .feature-icon {
            transform: scale(1.1) rotate(5deg);
            box-shadow: var(--shadow-xl);
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 1rem;
            position: relative;
            z-index: 2;
        }

        .feature-desc {
            color: var(--gray-600);
            font-size: 1rem;
            line-height: 1.7;
            position: relative;
            z-index: 2;
        }

        /* 主要内容区域 */
        .main-content {
            padding: 5rem 0;
            background: linear-gradient(135deg, #fafbff 0%, #f0f4ff 100%);
        }

        .content-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 3rem;
            align-items: start;
        }

        .form-wrapper {
            background: white;
            border-radius: var(--radius-2xl);
            padding: 3rem;
            box-shadow: var(--shadow-xl);
            border: 1px solid var(--gray-200);
            position: relative;
            overflow: hidden;
        }

        .form-wrapper::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--hero-gradient);
        }

        /* 历史记录侧边栏 */
        .history-sidebar {
            background: white;
            border-radius: var(--radius-2xl);
            padding: 2.5rem;
            box-shadow: var(--shadow-xl);
            border: 1px solid var(--gray-200);
            position: sticky;
            top: 140px;
            max-height: calc(100vh - 160px);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .history-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
            padding-bottom: 1.5rem;
            border-bottom: 2px solid var(--gray-100);
        }

        .history-icon {
            width: 56px;
            height: 56px;
            background: var(--hero-gradient);
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            box-shadow: var(--shadow-lg);
        }

        .history-title {
            font-size: 1.375rem;
            font-weight: 700;
            color: var(--gray-900);
        }

        .history-subtitle {
            font-size: 0.875rem;
            color: var(--gray-500);
            margin-top: 0.25rem;
        }

        .history-content {
            flex: 1;
            overflow-y: auto;
            margin: -0.5rem;
            padding: 0.5rem;
        }

        .history-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--feature-gradient);
            padding: 1.5rem;
            border-radius: var(--radius-xl);
            text-align: center;
            border: 1px solid rgba(99, 102, 241, 0.1);
            transition: all var(--transition-base);
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 800;
            color: var(--primary);
            display: block;
            line-height: 1;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--gray-600);
            margin-top: 0.5rem;
            font-weight: 500;
        }

        .history-item {
            background: var(--gray-50);
            padding: 1.25rem;
            border-radius: var(--radius-xl);
            cursor: pointer;
            transition: all var(--transition-base);
            border: 1px solid var(--gray-200);
            margin-bottom: 1rem;
            position: relative;
        }

        .history-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--hero-gradient);
            border-radius: var(--radius);
            transform: scaleY(0);
            transition: transform var(--transition-base);
        }

        .history-item:hover {
            background: white;
            border-color: var(--primary);
            transform: translateX(8px);
            box-shadow: var(--shadow-md);
        }

        .history-item:hover::before {
            transform: scaleY(1);
        }

        .history-item-title {
            font-weight: 600;
            color: var(--gray-900);
            font-size: 0.9rem;
            margin-bottom: 0.75rem;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            line-height: 1.4;
        }

        .history-item-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.75rem;
            color: var(--gray-500);
        }

        .history-item-date {
            display: flex;
            align-items: center;
            gap: 0.375rem;
        }

        .history-item-count {
            background: var(--primary);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: var(--radius);
            font-weight: 600;
            font-size: 0.75rem;
        }

        .empty-history {
            text-align: center;
            padding: 3rem 1rem;
            color: var(--gray-500);
        }

        .empty-history-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
            color: var(--gray-400);
        }

        /* 成功率展示 */
        .success-rate {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border-radius: var(--radius-2xl);
            padding: 3rem;
            text-align: center;
            margin: 2.5rem 0;
            box-shadow: var(--shadow-xl);
            position: relative;
            overflow: hidden;
        }

        .success-rate::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 20px,
                rgba(255,255,255,0.05) 20px,
                rgba(255,255,255,0.05) 40px
            );
            animation: shimmer 4s linear infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%); }
            100% { transform: translateX(0%) translateY(0%); }
        }

        .success-rate-content {
            position: relative;
            z-index: 2;
        }

        .success-rate-number {
            font-size: 4rem;
            font-weight: 900;
            display: block;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            color: white;
        }

        .success-rate-text {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .success-rate small {
            font-size: 0.95rem;
            opacity: 0.9;
        }

        /* 表单样式 */
        .form-section {
            margin-bottom: 3rem;
        }

        .form-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .form-title-icon {
            width: 40px;
            height: 40px;
            background: var(--hero-gradient);
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.125rem;
            box-shadow: var(--shadow-md);
        }

        .form-group {
            margin-bottom: 2rem;
            transition: all var(--transition-base);
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 0.75rem;
            font-size: 1rem;
        }

        .form-control, .form-select {
            width: 100%;
            padding: 1rem 1.25rem;
            border: 2px solid var(--gray-300);
            border-radius: var(--radius-xl);
            font-size: 1rem;
            background: white;
            transition: all var(--transition-base);
            font-family: inherit;
            color: var(--gray-900);
            box-shadow: var(--shadow-sm);
        }

        .form-control:focus, .form-select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
            transform: translateY(-1px);
        }

        .form-control::placeholder {
            color: var(--gray-400);
        }

        .textarea-large {
            min-height: 150px;
            resize: vertical;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }

        /* 生成按钮 */
        .generate-section {
            text-align: center;
            padding: 3rem 2rem;
            margin-top: 2rem;
            background: var(--feature-gradient);
            border-radius: var(--radius-2xl);
            border: 2px dashed var(--gray-300);
            transition: all var(--transition-base);
        }

        .generate-section:hover {
            border-color: var(--primary);
            background: rgba(99, 102, 241, 0.05);
        }

        .btn-generate {
            background: var(--hero-gradient);
            color: white;
            border: none;
            padding: 1.5rem 3rem;
            border-radius: var(--radius-2xl);
            font-size: 1.25rem;
            font-weight: 700;
            cursor: pointer;
            transition: all var(--transition-base);
            min-width: 300px;
            box-shadow: var(--shadow-xl);
            position: relative;
            overflow: hidden;
        }

        .btn-generate::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left var(--transition-slow);
        }

        .btn-generate:hover::before {
            left: 100%;
        }

        .btn-generate:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-2xl);
        }

        .btn-generate:disabled {
            background: var(--gray-400);
            cursor: not-allowed;
            transform: none;
            box-shadow: var(--shadow-sm);
        }

        /* 进度显示 */
        .progress-container {
            background: white;
            border: 2px solid var(--primary);
            border-radius: var(--radius-2xl);
            padding: 3rem;
            margin: 2rem 0;
            display: none;
            text-align: center;
            box-shadow: var(--shadow-xl);
        }

        .progress-icon {
            width: 100px;
            height: 100px;
            background: var(--hero-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2.5rem;
            margin: 0 auto 2rem;
            animation: pulse 2s infinite;
            box-shadow: var(--shadow-lg);
        }

        @keyframes pulse {
            0%, 100% { 
                transform: scale(1); 
                box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
            }
            50% { 
                transform: scale(1.05); 
                box-shadow: 0 0 0 20px rgba(99, 102, 241, 0);
            }
        }

        .progress-fill {
            width: 100%;
            height: 12px;
            background: var(--gray-200);
            border-radius: var(--radius);
            overflow: hidden;
            margin: 2rem 0;
        }

        .progress-bar {
            height: 100%;
            background: var(--hero-gradient);
            width: 0%;
            transition: width 0.8s ease;
            border-radius: var(--radius);
            position: relative;
        }

        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(255, 255, 255, 0.6),
                transparent
            );
            animation: progressShine 2s infinite;
        }

        @keyframes progressShine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* 结果展示 */
        .results-section {
            display: none;
            margin-top: 3rem;
        }

        .results-header {
            text-align: center;
            margin-bottom: 2rem;
            padding: 2.5rem;
            background: var(--feature-gradient);
            border-radius: var(--radius-2xl);
            border: 1px solid var(--gray-200);
        }

        .results-title {
            font-size: 2rem;
            font-weight: 800;
            color: var(--gray-900);
            margin-bottom: 1rem;
        }

        .results-subtitle {
            color: var(--gray-600);
            margin-bottom: 2rem;
            font-size: 1.125rem;
        }

        .results-actions {
            display: flex;
            justify-content: center;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .btn-export, .btn-copy {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border-radius: var(--radius-lg);
            font-weight: 600;
            text-decoration: none;
            transition: all var(--transition-base);
            cursor: pointer;
            border: none;
        }

        .btn-export {
            background: var(--primary);
            color: white;
        }

        .btn-copy {
            background: white;
            color: var(--primary);
            border: 2px solid var(--primary);
        }

        .btn-export:hover, .btn-copy:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-export:hover {
            background: var(--primary-dark);
        }

        .btn-copy:hover {
            background: var(--primary);
            color: white;
        }

        .question-card {
            background: white;
            border-radius: var(--radius-2xl);
            padding: 2.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--gray-200);
            transition: all var(--transition-base);
            position: relative;
            overflow: hidden;
        }

        .question-card::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--hero-gradient);
        }

        .question-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-2xl);
        }

        .question-header {
            display: flex;
            align-items: flex-start;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .question-number {
            width: 56px;
            height: 56px;
            background: var(--hero-gradient);
            color: white;
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 800;
            font-size: 1.375rem;
            flex-shrink: 0;
            box-shadow: var(--shadow-lg);
        }

        .question-content {
            flex: 1;
        }

        .question-category {
            background: var(--feature-gradient);
            color: var(--primary);
            padding: 0.5rem 1rem;
            border-radius: var(--radius);
            font-size: 0.875rem;
            font-weight: 700;
            display: inline-block;
            margin-bottom: 1rem;
            border: 1px solid rgba(99, 102, 241, 0.2);
        }

        .question-text {
            font-size: 1.375rem;
            font-weight: 700;
            color: var(--gray-900);
            line-height: 1.5;
        }

        .answer-section {
            background: var(--gray-50);
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-xl);
            padding: 2.5rem;
            margin-top: 2rem;
        }

        .answer-header {
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 1.5rem;
            font-size: 1.125rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .answer-text {
            color: var(--gray-700);
            line-height: 1.8;
            font-size: 1rem;
        }

        /* 答案序号点样式 */
        .answer-point {
            display: flex;
            align-items: flex-start;
            margin: 1rem 0;
            padding: 0.75rem;
            background: rgba(99, 102, 241, 0.03);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }

        .point-number {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 2rem;
            height: 2rem;
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
            color: white;
            border-radius: 50%;
            font-weight: 700;
            font-size: 0.875rem;
            margin-right: 1rem;
            flex-shrink: 0;
            box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
        }

        .point-content {
            flex: 1;
            padding-top: 0.25rem;
            line-height: 1.6;
            color: var(--gray-700);
        }

        .answer-point > span:not(.point-number) {
            flex: 1;
            padding-top: 0.25rem;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .content-container {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
            
            .history-sidebar {
                position: static;
                max-height: none;
            }
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
                padding: 0 1rem;
            }
            
            .form-wrapper, .history-sidebar {
                padding: 2rem;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .trust-indicators {
                gap: 1rem;
            }
            
            .trust-item {
                padding: 0.75rem 1.5rem;
                font-size: 0.9rem;
            }

            .question-header {
                gap: 1.5rem;
            }

            .question-number {
                width: 48px;
                height: 48px;
                font-size: 1.125rem;
            }

            .question-text {
                font-size: 1.125rem;
            }
        }

        /* 加载动画 */
        .loading {
            pointer-events: none;
        }

        .loading .btn-generate {
            position: relative;
            color: transparent;
        }

        .loading .btn-generate::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 24px;
            height: 24px;
            margin-left: -12px;
            margin-top: -12px;
            border: 3px solid #ffffff;
            border-top: 3px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 通知系统 */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            min-width: 400px;
            max-width: 500px;
            z-index: 10000;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-2xl);
            overflow: hidden;
            transform: translateX(120%);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            opacity: 0;
            backdrop-filter: blur(20px);
        }

        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        .notification-content {
            display: flex;
            align-items: center;
            padding: 1.5rem;
            color: white;
        }

        .notification-icon {
            font-size: 1.5rem;
            margin-right: 1rem;
            flex-shrink: 0;
        }

        .notification-text h4 {
            margin: 0 0 0.5rem 0;
            font-size: 1.125rem;
            font-weight: 700;
        }

        .notification-text p {
            margin: 0;
            font-size: 0.95rem;
            opacity: 0.95;
            line-height: 1.4;
        }

        .notification.success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        .notification.error {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }

        .notification.warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease both;
        }

        /* 微交互效果 */
        .interactive-element {
            transition: all var(--transition-base);
        }

        .interactive-element:hover {
            transform: translateY(-2px);
        }

        /* 高级样式 */
        .glass-effect {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px) saturate(180%);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .gradient-text {
            background: var(--hero-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <nav class="top-nav">
        <div class="nav-container">
            <a href="/" class="back-btn">
                <i class="fas fa-arrow-left"></i>
                <span>返回首页</span>
            </a>
            
        </div>
    </nav>

    <!-- 英雄区域 -->
    <section class="hero-section">
        <div class="hero-content">
            <h1 class="hero-title">AI论文答辩预测器</h1>
            <p class="hero-subtitle">基于深度学习算法，精准预测答辩委员会提问内容<br>让你提前掌握答辩节奏，轻松应对每一个问题</p>
            
            <div class="trust-indicators">
                <div class="trust-item">
                    <i class="fas fa-brain"></i>
                    智能AI分析
                </div>
                <div class="trust-item">
                    <i class="fas fa-bullseye"></i>
                    精准问题预测
                </div>
                <div class="trust-item">
                    <i class="fas fa-graduation-cap"></i>
                    答辩助手工具
                </div>
            </div>
        </div>
    </section>

    <!-- 功能特点 -->
    <section class="features-section">
        <div class="section-header">
            <h2 class="section-title">功能特点</h2>
            <p class="section-subtitle">基于深度学习的智能答辩问题预测系统</p>
        </div>
        
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-brain"></i>
                </div>
                <h3 class="feature-title">AI深度分析</h3>
                <p class="feature-desc">基于10万+答辩数据训练，深度学习论文核心内容，精准识别老师关注重点</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-bullseye"></i>
                </div>
                <h3 class="feature-title">精准预测</h3>
                <p class="feature-desc">98.5%的问题命中率，提前预知答辩委员会可能提出的所有问题</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-cogs"></i>
                </div>
                <h3 class="feature-title">个性化定制</h3>
                <p class="feature-desc">根据你的专业领域、研究方向，量身定制答辩问题库</p>
            </div>
        </div>
    </section>

    <!-- 主要内容 -->
    <section class="main-content">
        <div class="content-container">
            <!-- 表单区域 -->
            <div class="form-wrapper">
                <div class="section-header">
                    <h1 class="section-title">开始生成你的专属答辩问题</h1>
                    <p class="section-subtitle">填写论文基本信息，AI将为你生成最可能被问到的答辩问题</p>
                </div>

                <!-- 成功率展示 -->
                <div class="success-rate">
                    <div class="success-rate-content">
                        <span class="success-rate-number"><i class="fas fa-magic"></i></span>
                        <div class="success-rate-text">AI智能分析，精准预测答辩问题</div>
                        <small>基于论文内容生成专业答辩问题，助您充分准备</small>
                    </div>
                </div>

                <!-- 表单开始 -->
                <form id="defenseForm">
                    <!-- 基本信息 -->
                    <div class="form-section">
                        <h3 class="form-title">
                            <div class="form-title-icon">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            基本信息
                        </h3>
                        
                        <div class="form-group">
                            <label class="form-label">论文题目 *</label>
                            <input type="text" class="form-control" id="thesisTitle" 
                                   placeholder="例：基于深度学习的智能推荐系统设计与实现" required>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">研究领域 *</label>
                                <input type="text" class="form-control" id="researchField" 
                                       placeholder="例：计算机科学与技术" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">难度级别</label>
                                <select class="form-select" id="difficulty">
                                    <option value="basic">本科答辩</option>
                                    <option value="intermediate" selected>硕士答辩</option>
                                    <option value="advanced">博士答辩</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">论文摘要 *</label>
                            <textarea class="form-control textarea-large" id="thesisAbstract" required
                                      placeholder="请粘贴你的论文摘要，包括研究背景、方法、结果和结论..."></textarea>
                        </div>
                    </div>

                    <!-- 系统信息（可选） -->
                    <div class="form-section">
                        <h3 class="form-title">
                            <div class="form-title-icon">
                                <i class="fas fa-server"></i>
                            </div>
                            系统/项目信息（可选）
                        </h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">系统名称</label>
                                <input type="text" class="form-control" id="systemName" placeholder="系统名称">
                            </div>
                            <div class="form-group">
                                <label class="form-label">技术栈</label>
                                <input type="text" class="form-control" id="techStack" placeholder="技术栈">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">系统功能描述</label>
                            <textarea class="form-control" id="systemDescription" rows="3"
                                      placeholder="系统功能描述（可选）"></textarea>
                        </div>
                    </div>

                    <!-- 生成按钮 -->
                    <div class="generate-section">
                        <button type="submit" class="btn-generate" id="generateBtn">
                            <i class="fas fa-rocket me-2"></i>
                            <span id="generateBtnText">开始AI智能预测</span>
                        </button>
                        <div style="margin-top: 1rem; color: var(--gray-600); font-size: 0.95rem;">
                            ⚡ 限时免费体验，原价299元/次
                        </div>
                    </div>
                </form>

                <!-- 进度显示 -->
                <div class="progress-container" id="progressContainer">
                    <div class="progress-icon">
                        <i class="fas fa-brain" id="progressIcon"></i>
                    </div>
                    <div style="font-size: 1.5rem; font-weight: 700; color: var(--gray-900); margin-bottom: 0.5rem;" id="progressTitle">AI正在深度分析你的论文...</div>
                    <div style="color: var(--gray-600); margin-bottom: 2rem;" id="progressDetail">这可能需要30-60秒，请耐心等待</div>
                    
                    <!-- 等待提示 -->
                    <div style="background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); border: 1px solid #f59e0b; border-radius: 12px; padding: 1.5rem; margin: 1.5rem 0; text-align: left;">
                        <div style="display: flex; align-items: center; gap: 0.75rem; margin-bottom: 1rem;">
                            <i class="fas fa-exclamation-triangle" style="color: #f59e0b; font-size: 1.25rem;"></i>
                            <span style="font-weight: 700; color: #92400e; font-size: 1.1rem;">请注意</span>
                        </div>
                        <ul style="color: #92400e; font-size: 0.95rem; line-height: 1.6; margin: 0; padding-left: 1.5rem;">
                            <li style="margin-bottom: 0.5rem;">AI分析需要一定时间，请<strong>不要关闭或刷新页面</strong></li>
                            <li style="margin-bottom: 0.5rem;">生成过程中请保持网络连接稳定</li>
                            <li>完成后会自动显示您的专属答辩问题</li>
                        </ul>
                    </div>
                    
                    <div class="progress-fill">
                        <div class="progress-bar" id="progressBar"></div>
                    </div>
                    <div style="font-size: 1.375rem; color: var(--primary); font-weight: 700;" id="progressPercent">0%</div>
                </div>

                <!-- 结果展示 -->
                <div class="results-section" id="resultsSection">
                    <div class="results-header">
                        <h2 class="results-title">🎉 AI预测完成！以下是你的专属答辩问题</h2>
                        <p class="results-subtitle">建议打印或收藏，提前准备答案</p>
                        <div class="results-actions">
                            <button class="btn-export" onclick="exportToWord()">
                                <i class="fas fa-download"></i> 导出Word
                            </button>
                            <button class="btn-copy" onclick="copyToClipboard()">
                                <i class="fas fa-copy"></i> 复制全部
                            </button>
                        </div>
                    </div>
                    <div id="questionsContainer"></div>
                </div>
            </div>

            <!-- 历史记录侧边栏 -->
            <div class="history-sidebar">
                <div class="history-header">
                    <div class="history-icon">
                        <i class="fas fa-history"></i>
                    </div>
                    <div>
                        <div class="history-title">历史记录</div>
                        <div class="history-subtitle">生成问题后会自动保存</div>
                    </div>
                </div>

                <div class="history-stats">
                    <div class="stat-card">
                        <span class="stat-number" id="totalGenerations">0</span>
                        <div class="stat-label">总生成次数</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number" id="totalQuestions">0</span>
                        <div class="stat-label">总问题数</div>
                    </div>
                </div>

                <div class="history-content">
                    <div class="history-list" id="historyList">
                        <div class="empty-history">
                            <div class="empty-history-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div style="font-weight: 600; margin-bottom: 0.5rem;">暂无历史记录</div>
                            <div style="font-size: 0.85rem;">生成问题后会自动保存</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let currentQuestions = [];
        let isGenerating = false;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupFormValidation();
            loadHistoryRecords();
            initializeInteractiveElements();
        });

        // 初始化交互元素
        function initializeInteractiveElements() {
            // 添加表单输入焦点效果
            const inputs = document.querySelectorAll('.form-control, .form-select');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'translateY(-2px)';
                });
                
                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'translateY(0)';
                });
            });

            // 添加卡片悬停效果
            const cards = document.querySelectorAll('.feature-card, .stat-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        }

        // 表单验证设置
        function setupFormValidation() {
            const form = document.getElementById('defenseForm');
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                if (isGenerating) {
                    showNotification('正在生成中，请稍候...', 'warning');
                    return;
                }
                
                // 基本验证
                const title = document.getElementById('thesisTitle').value.trim();
                const field = document.getElementById('researchField').value.trim();
                const abstract = document.getElementById('thesisAbstract').value.trim();
                
                if (!title || !field || !abstract) {
                    showNotification('请填写所有必填项', 'error');
                    return;
                }
                
                if (title.length < 5) {
                    showNotification('论文题目长度至少5个字符', 'error');
                    return;
                }
                
                if (abstract.length < 100) {
                    showNotification('论文摘要长度至少100个字符', 'error');
                    return;
                }
                
                // 开始生成
                startGeneration();
            });
        }

        // 开始生成
        async function startGeneration() {
            isGenerating = true;
            document.body.classList.add('loading');
            
            // 添加页面离开前的提醒
            window.addEventListener('beforeunload', preventPageLeave);
            
            // 隐藏之前的结果
            document.getElementById('resultsSection').style.display = 'none';
            
            // 显示进度
            showProgress();
            
            // 收集表单数据
            const formData = {
                thesisTitle: document.getElementById('thesisTitle').value.trim(),
                researchField: document.getElementById('researchField').value.trim(),
                thesisAbstract: document.getElementById('thesisAbstract').value.trim(),
                systemName: document.getElementById('systemName').value.trim(),
                techStack: document.getElementById('techStack').value.trim(),
                systemDescription: document.getElementById('systemDescription').value.trim(),
                difficultyLevel: document.getElementById('difficulty').value,
                questionCount: 25,
                mode: 'single'
            };
            
            try {
                // 开始进度模拟，但不完成到100%
                simulateProgress();
                
                // 调用API
                const response = await fetch('/api/generate-defense-questions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });
                
                const result = await response.json();
                
                if (result.success && result.questions && result.questions.length > 0) {
                    // 只有在真正获得结果时才完成进度条
                    completeProgress();
                    
                    // 短暂延迟后显示结果
                    setTimeout(() => {
                        displayResults(result.questions);
                        showNotification(`🎉 AI预测完成！共生成 ${result.questions.length} 个精准答辩问题`, 'success');
                        hideProgress();
                        
                        // 更新历史记录
                        addToHistory(formData, result.questions);
                    }, 800);
                } else {
                    throw new Error(result.message || '生成失败，请重试');
                }
            } catch (error) {
                console.error('Generation error:', error);
                showError(error.message || '网络错误，请重试');
            } finally {
                setTimeout(() => {
                    isGenerating = false;
                    document.body.classList.remove('loading');
                    // 移除页面离开前的提醒
                    window.removeEventListener('beforeunload', preventPageLeave);
                }, 1000);
            }
        }

        // 防止页面离开的函数
        function preventPageLeave(e) {
            if (isGenerating) {
                e.preventDefault();
                e.returnValue = 'AI正在生成答辩问题，离开页面将丢失进度。确定要离开吗？';
                return 'AI正在生成答辩问题，离开页面将丢失进度。确定要离开吗？';
            }
        }

        // 显示进度
        function showProgress() {
            document.getElementById('progressContainer').style.display = 'block';
            document.getElementById('progressContainer').scrollIntoView({ behavior: 'smooth' });
        }

        // 隐藏进度
        function hideProgress() {
            document.getElementById('progressContainer').style.display = 'none';
        }

        // 模拟进度 - 修复版本，不会自动到100%
        function simulateProgress() {
            let progress = 0;
            const progressBar = document.getElementById('progressBar');
            const progressPercent = document.getElementById('progressPercent');
            const progressTitle = document.getElementById('progressTitle');
            const progressDetail = document.getElementById('progressDetail');
            const progressIcon = document.getElementById('progressIcon');
            
            const steps = [
                { percent: 15, title: "🔍 解析论文结构...", detail: "AI正在分析论文的章节结构和核心内容", icon: "fas fa-search" },
                { percent: 35, title: "🧠 识别关键技术点...", detail: "深度学习算法正在提取技术关键词和创新点", icon: "fas fa-brain" },
                { percent: 60, title: "🎯 预测提问角度...", detail: "基于答辩委员会习惯，预测可能的提问方向", icon: "fas fa-bullseye" },
                { percent: 85, title: "💡 生成答案中...", detail: "正在为每个问题生成专业的参考答案", icon: "fas fa-lightbulb" },
                { percent: 95, title: "🔄 最后处理中...", detail: "即将完成，正在优化问题质量", icon: "fas fa-sync-alt" }
            ];
            
            let currentStep = 0;
            
            const interval = setInterval(() => {
                if (currentStep < steps.length) {
                    const step = steps[currentStep];
                    progress = step.percent;
                    
                    progressBar.style.width = progress + '%';
                    progressPercent.textContent = progress + '%';
                    progressTitle.textContent = step.title;
                    progressDetail.textContent = step.detail;
                    progressIcon.className = step.icon;
                    
                    currentStep++;
                } else {
                    // 停在95%，等待真实结果
                    clearInterval(interval);
                }
            }, 1500);
        }

        // 完成进度条
        function completeProgress() {
            document.getElementById('progressBar').style.width = '100%';
            document.getElementById('progressPercent').textContent = '100%';
            document.getElementById('progressTitle').textContent = '✅ 预测完成！';
            document.getElementById('progressDetail').textContent = '正在为您展示结果...';
        }

        // 显示错误
        function showError(message) {
            hideProgress();
            showNotification('生成失败：' + message, 'error');
        }

        // 显示结果
        function displayResults(questions) {
            currentQuestions = questions;
            const container = document.getElementById('questionsContainer');
            
            container.innerHTML = questions.map((q, index) => `
                <div class="question-card fade-in-up" style="animation-delay: ${index * 0.1}s;">
                    <div class="question-header">
                        <div class="question-number">${index + 1}</div>
                        <div class="question-content">
                            <div class="question-category">${q.category || '综合问题'}</div>
                            <div class="question-text">${q.question}</div>
                        </div>
                    </div>
                    <div class="answer-section">
                        <div class="answer-header">
                            <span>💡</span> 参考答案
                        </div>
                        <div class="answer-text">${formatAnswer(q.answer)}</div>
                    </div>
                </div>
            `).join('');
            
            document.getElementById('resultsSection').style.display = 'block';
            
            // 滚动到结果区域
            setTimeout(() => {
                document.getElementById('resultsSection').scrollIntoView({ 
                    behavior: 'smooth',
                    block: 'start'
                });
            }, 500);
        }

        // 格式化答案 - 优化版本，改进数字序号排版
        function formatAnswer(answer) {
            if (!answer) return '';
            
            // 先处理换行符
            let formattedAnswer = answer.replace(/\n/g, '<br>');
            
            // 处理数字序号（1. 2. 3. 等）
            formattedAnswer = formattedAnswer.replace(/(\d+)\.\s*([^<\n]+)/g, 
                '<div class="answer-point"><span class="point-number">$1</span><span class="point-content">$2</span></div>');
            
            // 处理中文序号（一、二、三、等）
            formattedAnswer = formattedAnswer.replace(/([一二三四五六七八九十]+)、\s*([^<\n]+)/g, 
                '<div class="answer-point"><span class="point-number">$1</span><span class="point-content">$2</span></div>');
            
            // 处理带括号的序号 (1) (2) (3) 等
            formattedAnswer = formattedAnswer.replace(/\((\d+)\)\s*([^<\n]+)/g, 
                '<div class="answer-point"><span class="point-number">$1</span><span class="point-content">$2</span></div>');
            
            // 处理冒号，增加适当的间距
            formattedAnswer = formattedAnswer.replace(/：/g, '：<span style="margin-left: 0.5rem;"></span>');
            
            return formattedAnswer;
        }

        // 显示通知
        function showNotification(message, type = 'success') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            
            const icons = {
                success: 'fas fa-check-circle',
                error: 'fas fa-exclamation-triangle',
                warning: 'fas fa-exclamation-circle'
            };
            
            const titles = {
                success: '成功',
                error: '错误',
                warning: '警告'
            };

            notification.innerHTML = `
                <div class="notification-content">
                    <div class="notification-icon">
                        <i class="${icons[type]}"></i>
                    </div>
                    <div class="notification-text">
                        <h4>${titles[type]}</h4>
                        <p>${message}</p>
                    </div>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);
            
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 400);
            }, 5000);
        }

        // 历史记录功能
        function loadHistoryRecords() {
            // 从localStorage获取历史记录
            const history = JSON.parse(localStorage.getItem('defenseHistory') || '[]');
            updateHistoryDisplay(history);
            updateHistoryStats(history);
        }

        function updateHistoryDisplay(history) {
            const historyList = document.getElementById('historyList');
            
            if (history.length === 0) {
                historyList.innerHTML = `
                    <div class="empty-history">
                        <div class="empty-history-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div style="font-weight: 600; margin-bottom: 0.5rem;">暂无历史记录</div>
                        <div style="font-size: 0.85rem;">生成问题后会自动保存</div>
                    </div>
                `;
                return;
            }

            historyList.innerHTML = history.slice(0, 10).map((record, index) => `
                <div class="history-item" onclick="loadHistoryRecord(${index})">
                    <div class="history-item-title">${record.thesisTitle}</div>
                    <div class="history-item-meta">
                        <div class="history-item-date">
                            <i class="fas fa-clock"></i>
                            ${new Date(record.timestamp).toLocaleDateString()}
                        </div>
                        <div class="history-item-count">${record.questions.length}题</div>
                    </div>
                </div>
            `).join('');
        }

        function updateHistoryStats(history) {
            const totalGenerations = history.length;
            const totalQuestions = history.reduce((sum, record) => sum + record.questions.length, 0);
            
            document.getElementById('totalGenerations').textContent = totalGenerations;
            document.getElementById('totalQuestions').textContent = totalQuestions;
        }

        function addToHistory(formData, questions) {
            const history = JSON.parse(localStorage.getItem('defenseHistory') || '[]');
            const newRecord = {
                ...formData,
                questions: questions,
                timestamp: new Date().toISOString()
            };
            
            history.unshift(newRecord);
            
            // 只保留最近50条记录
            if (history.length > 50) {
                history.splice(50);
            }
            
            localStorage.setItem('defenseHistory', JSON.stringify(history));
            loadHistoryRecords();
        }

        function loadHistoryRecord(index) {
            const history = JSON.parse(localStorage.getItem('defenseHistory') || '[]');
            const record = history[index];
            
            if (!record) return;
            
            // 恢复表单数据
            document.getElementById('thesisTitle').value = record.thesisTitle || '';
            document.getElementById('researchField').value = record.researchField || '';
            document.getElementById('thesisAbstract').value = record.thesisAbstract || '';
            document.getElementById('systemName').value = record.systemName || '';
            document.getElementById('techStack').value = record.techStack || '';
            document.getElementById('systemDescription').value = record.systemDescription || '';
            document.getElementById('difficulty').value = record.difficultyLevel || 'intermediate';
            
            // 显示历史问题
            displayResults(record.questions);
            
            showNotification('历史记录已恢复', 'success');
        }

        // 导出Word
        async function exportToWord() {
            if (currentQuestions.length === 0) {
                showNotification('请先生成答辩问题', 'error');
                return;
            }
            
            try {
                showNotification('正在生成Word文档...', 'warning');
                
                const response = await fetch('/api/export-defense-questions-word', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        questions: currentQuestions,
                        thesisTitle: document.getElementById('thesisTitle').value,
                        researchField: document.getElementById('researchField').value
                    })
                });
                
                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = '论文答辩问题预测.docx';
                    a.click();
                    window.URL.revokeObjectURL(url);
                    showNotification('Word文档下载成功！', 'success');
                } else {
                    throw new Error('导出失败');
                }
            } catch (error) {
                showNotification('导出失败：' + error.message, 'error');
            }
        }

        // 复制到剪贴板
        function copyToClipboard() {
            if (currentQuestions.length === 0) {
                showNotification('请先生成答辩问题', 'error');
                return;
            }
            
            const text = currentQuestions.map((q, i) => 
                `${i + 1}. 【${q.category}】${q.question}\\n参考答案：${q.answer}\\n`
            ).join('\\n');
            
            navigator.clipboard.writeText(text).then(() => {
                showNotification('内容已复制到剪贴板！', 'success');
            }).catch(() => {
                showNotification('复制失败，请手动复制', 'error');
            });
        }
    </script>
</body>
</html>