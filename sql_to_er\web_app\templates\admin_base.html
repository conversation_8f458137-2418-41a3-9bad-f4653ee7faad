<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台 - SQL转ER图工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            height: 100vh;
            background: #343a40;
            position: fixed;
            top: 0;
            left: 0;
            width: 250px;
            z-index: 1000;
        }
        .sidebar .nav-link {
            color: #adb5bd;
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #495057;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            color: white;
            background-color: #495057;
        }
        .main-content {
            margin-left: 250px;
            padding: 20px;
            min-height: 100vh;
        }
        .stats-card {
            transition: transform 0.2s;
        }
        .stats-card:hover {
            transform: translateY(-2px);
        }
        .navbar-admin {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .card-header-admin {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .btn-admin {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
        }
        .btn-admin:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            color: white;
        }
        .status-badge {
            font-size: 0.8em;
            padding: 4px 8px;
        }
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s;
            }
            .sidebar.show {
                transform: translateX(0);
            }
            .main-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar d-flex flex-column">
        <div class="p-3 text-center border-bottom border-secondary">
            <h5 class="text-white mb-0">
                <i class="bi bi-shield-lock"></i>
                管理后台
            </h5>
            <small class="text-muted">Administrator Panel</small>
        </div>
        
        <ul class="nav nav-pills flex-column mt-3">
            <li class="nav-item">
                <a class="nav-link {% if active_page == 'dashboard' %}active{% endif %}" href="{{ url_for('admin_dashboard') }}">
                    <i class="bi bi-speedometer2 me-2"></i>
                    仪表盘
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if active_page == 'users' %}active{% endif %}" href="{{ url_for('admin_users') }}">
                    <i class="bi bi-people me-2"></i>
                    用户管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if active_page == 'finance' %}active{% endif %}" href="{{ url_for('admin_finance') }}">
                    <i class="bi bi-credit-card me-2"></i>
                    财务管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if active_page == 'logs' %}active{% endif %}" href="{{ url_for('admin_logs') }}">
                    <i class="bi bi-journal-text me-2"></i>
                    日志管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if active_page == 'system' %}active{% endif %}" href="{{ url_for('admin_system') }}">
                    <i class="bi bi-gear me-2"></i>
                    系统设置
                </a>
            </li>
            <li class="nav-item mt-auto">
                <a class="nav-link text-danger" href="{{ url_for('homepage') }}">
                    <i class="bi bi-box-arrow-left me-2"></i>
                    返回前台
                </a>
            </li>
        </ul>
        
        <div class="p-3 border-top border-secondary mt-auto">
            <div class="text-muted small">
                <i class="bi bi-person-circle me-1"></i>
                {{ user_info.username if user_info else 'Admin' }}
            </div>
        </div>
    </nav>

    <!-- 主内容区 -->
    <div class="main-content">
        <!-- 顶部导航栏 -->
        <nav class="navbar navbar-expand-lg navbar-admin rounded mb-4">
            <div class="container-fluid">
                <button class="navbar-toggler d-lg-none" type="button" id="sidebarToggle">
                    <i class="bi bi-list text-white"></i>
                </button>
                
                <h4 class="mb-0 text-white">
                    {% block page_title %}管理后台{% endblock %}
                </h4>
                
                <div class="navbar-nav ms-auto">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle text-white" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i>
                            {{ user_info.username if user_info else 'Admin' }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('profile') }}">个人中心</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="#" onclick="logout()">退出登录</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        <!-- 页面内容 -->
        <div class="container-fluid">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            {% block content %}{% endblock %}
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // 侧边栏切换（移动端）
        document.getElementById('sidebarToggle')?.addEventListener('click', function() {
            document.querySelector('.sidebar').classList.toggle('show');
        });

        // 退出登录
        async function logout() {
            if (confirm('确定要退出登录吗？')) {
                try {
                    const response = await fetch('/api/logout', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    });
                    
                    if (response.ok) {
                        window.location.href = '/login';
                    } else {
                        alert('退出登录失败');
                    }
                } catch (error) {
                    console.error('退出登录失败:', error);
                    alert('网络错误，请重试');
                }
            }
        }

        // 通用AJAX请求函数
        async function apiRequest(url, method = 'GET', data = null) {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (data) {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(url, options);
                return await response.json();
            } catch (error) {
                console.error('API请求失败:', error);
                throw error;
            }
        }

        // 格式化数字
        function formatNumber(num) {
            return new Intl.NumberFormat('zh-CN').format(num);
        }

        // 格式化货币
        function formatCurrency(amount) {
            return new Intl.NumberFormat('zh-CN', {
                style: 'currency',
                currency: 'CNY'
            }).format(amount);
        }

        // 格式化日期
        function formatDate(dateString) {
            return new Date(dateString).toLocaleString('zh-CN');
        }
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>