-- 用户系统安全和管理员功能升级脚本
-- 执行前请先备份数据库

USE user_system;

-- 1. 为users表添加管理员相关字段
ALTER TABLE users 
ADD COLUMN role ENUM('user', 'admin') DEFAULT 'user' COMMENT '用户角色：user普通用户，admin管理员' AFTER status,
ADD COLUMN last_login_ip VARCHAR(45) DEFAULT NULL COMMENT '最后登录IP地址' AFTER last_login_at,
ADD INDEX idx_role (role);

-- 2. 创建管理员操作日志表
CREATE TABLE admin_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    admin_id INT NOT NULL COMMENT '管理员ID',
    action VARCHAR(50) NOT NULL COMMENT '操作类型：update_user_status, add_balance等',
    target_type VARCHAR(20) NOT NULL COMMENT '操作对象类型：user, system等',
    target_id INT DEFAULT NULL COMMENT '操作对象ID',
    old_data JSON DEFAULT NULL COMMENT '操作前数据',
    new_data JSON DEFAULT NULL COMMENT '操作后数据',
    description TEXT DEFAULT NULL COMMENT '操作描述',
    ip_address VARCHAR(45) DEFAULT NULL COMMENT '操作IP地址',
    user_agent TEXT DEFAULT NULL COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_admin_id (admin_id),
    INDEX idx_action (action),
    INDEX idx_target (target_type, target_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员操作日志表';

-- 3. 创建登录日志表（用于安全审计）
CREATE TABLE login_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT DEFAULT NULL COMMENT '用户ID（登录成功时记录）',
    username VARCHAR(50) NOT NULL COMMENT '登录用户名',
    ip_address VARCHAR(45) NOT NULL COMMENT '登录IP地址',
    user_agent TEXT DEFAULT NULL COMMENT '用户代理',
    status ENUM('success', 'failed', 'blocked') NOT NULL COMMENT '登录状态',
    failure_reason VARCHAR(100) DEFAULT NULL COMMENT '失败原因',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_username (username),
    INDEX idx_ip_address (ip_address),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='登录日志表';

-- 4. 创建系统安全配置表
CREATE TABLE security_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    description VARCHAR(255) DEFAULT NULL COMMENT '配置描述',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用：1启用，0禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_config_key (config_key),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='安全配置表';

-- 5. 插入默认安全配置
INSERT INTO security_config (config_key, config_value, description) VALUES
('max_login_attempts', '5', '最大登录尝试次数'),
('lockout_duration', '900', '账户锁定时长（秒）'),
('session_timeout', '7200', 'Session超时时间（秒）'),
('password_min_length', '6', '密码最小长度'),
('enable_ip_check', '0', '是否启用IP检查（0禁用，1启用）'),
('admin_session_timeout', '3600', '管理员Session超时时间（秒）');

-- 6. 创建默认管理员用户（请在生产环境中修改密码）
-- 密码是：admin123456 的bcrypt哈希值
-- 注意：这里使用的是示例哈希值，实际部署时需要重新生成
INSERT INTO users (username, password_hash, balance, invite_code, role, status, created_at) 
VALUES ('admin', '$2b$12$rWvNnVwYE8GhPZFJ8LuuQ.ZM4DH8xzH5QJT7CsYiWWlPzf8GzE6oe', 0.00, 'ADMIN001', 'admin', 1, NOW())
ON DUPLICATE KEY UPDATE role = 'admin';

-- 7. 更新系统配置表，添加管理员相关配置
INSERT INTO system_config (config_key, config_value, description) VALUES
('admin_invite_reward', '0.00', '管理员邀请奖励（通常为0）'),
('admin_operations_log', '1', '是否记录管理员操作日志'),
('user_registration_enabled', '1', '是否允许用户注册'),
('require_invite_code', '0', '是否强制要求邀请码注册')
ON DUPLICATE KEY UPDATE config_value = VALUES(config_value);

-- 8. 创建用户统计视图（便于管理员查看）
CREATE OR REPLACE VIEW user_statistics AS
SELECT 
    DATE(created_at) as date,
    COUNT(*) as new_users,
    COUNT(CASE WHEN status = 1 THEN 1 END) as active_users,
    SUM(balance) as total_balance,
    SUM(total_recharge) as total_recharge,
    SUM(total_consumption) as total_consumption
FROM users 
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- 9. 创建今日数据统计视图
CREATE OR REPLACE VIEW today_statistics AS
SELECT 
    'users' as metric,
    COUNT(*) as total,
    COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today
FROM users
UNION ALL
SELECT 
    'consumption' as metric,
    COUNT(*) as total,
    COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today
FROM consumption_records
UNION ALL
SELECT 
    'recharge' as metric,
    COUNT(*) as total,
    COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today
FROM recharge_records WHERE status = 1;

-- 10. 创建用户活跃度统计存储过程
DELIMITER //
CREATE PROCEDURE GetUserActivityStats(IN days_range INT)
BEGIN
    SELECT 
        DATE(created_at) as date,
        COUNT(*) as new_registrations,
        (SELECT COUNT(*) FROM users u2 WHERE DATE(u2.last_login_at) = DATE(users.created_at)) as active_logins,
        (SELECT COALESCE(SUM(cr.amount), 0) FROM consumption_records cr WHERE DATE(cr.created_at) = DATE(users.created_at)) as daily_consumption
    FROM users 
    WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL days_range DAY)
    GROUP BY DATE(created_at)
    ORDER BY date DESC;
END //
DELIMITER ;

-- 11. 创建管理员操作日志记录存储过程
DELIMITER //
CREATE PROCEDURE LogAdminAction(
    IN p_admin_id INT,
    IN p_action VARCHAR(50),
    IN p_target_type VARCHAR(20),
    IN p_target_id INT,
    IN p_old_data JSON,
    IN p_new_data JSON,
    IN p_description TEXT,
    IN p_ip_address VARCHAR(45),
    IN p_user_agent TEXT
)
BEGIN
    INSERT INTO admin_logs (
        admin_id, action, target_type, target_id, 
        old_data, new_data, description, 
        ip_address, user_agent
    ) VALUES (
        p_admin_id, p_action, p_target_type, p_target_id,
        p_old_data, p_new_data, p_description,
        p_ip_address, p_user_agent
    );
END //
DELIMITER ;

-- 12. 添加触发器，自动记录用户状态变更
DELIMITER //
CREATE TRIGGER after_user_status_update
AFTER UPDATE ON users
FOR EACH ROW
BEGIN
    IF OLD.status != NEW.status THEN
        INSERT INTO admin_logs (admin_id, action, target_type, target_id, old_data, new_data, description)
        VALUES (
            COALESCE(@current_admin_id, 0),
            'update_user_status',
            'user',
            NEW.id,
            JSON_OBJECT('status', OLD.status),
            JSON_OBJECT('status', NEW.status),
            CONCAT('用户状态从 ', OLD.status, ' 变更为 ', NEW.status)
        );
    END IF;
END //
DELIMITER ;

-- 13. 创建清理过期登录日志的事件（可选）
-- SET GLOBAL event_scheduler = ON;
-- 
-- CREATE EVENT IF NOT EXISTS cleanup_old_login_logs
-- ON SCHEDULE EVERY 1 DAY
-- STARTS CURRENT_TIMESTAMP
-- DO
--   DELETE FROM login_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);

-- 升级完成提示
SELECT '数据库升级完成！' as message,
       '已添加管理员角色、安全日志等功能' as details,
       '默认管理员账号：admin，密码：admin123456（请立即修改！）' as admin_info;