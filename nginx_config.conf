# Nginx配置文件示例
# 在宝塔面板中配置网站时使用

server {
    listen 80;
    server_name ybcybcybc.xyz www.ybcybcybc.xyz;  # 替换为实际域名
    
    # 如果有SSL证书，添加以下配置
    # listen 443 ssl http2;
    # ssl_certificate /path/to/your/certificate.crt;
    # ssl_certificate_key /path/to/your/private.key;
    
    # 网站根目录
    root /www/wwwroot/ybcybcybc.xyz/sql4;
    index index.html index.htm;
    
    # 日志文件
    access_log /www/wwwlogs/ybcybcybc.xyz.log;
    error_log /www/wwwlogs/ybcybcybc.xyz.error.log;
    
    # 静态文件处理
    location /static/ {
        alias /www/wwwroot/ybcybcybc.xyz/sql4/sql_to_er/web_app/static/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
    
    # 上传文件处理
    location /uploads/ {
        alias /www/wwwroot/ybcybcybc.xyz/sql4/uploads/;
        expires 7d;
    }
    
    # 代理到Flask应用
    location / {
        proxy_pass http://127.0.0.1:5001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 8k;
        proxy_buffers 8 8k;
    }
    
    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
}