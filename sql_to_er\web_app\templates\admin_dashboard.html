{% extends "admin_base.html" %}

{% block page_title %}仪表盘{% endblock %}

{% block content %}
<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow stats-card h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            总用户数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-users">
                            {{ stats.total_users if stats else 0 }}
                        </div>
                    </div>
                    <div class="text-primary">
                        <i class="bi bi-people fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow stats-card h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            活跃用户
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="active-users">
                            {{ stats.active_users if stats else 0 }}
                        </div>
                    </div>
                    <div class="text-success">
                        <i class="bi bi-person-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow stats-card h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            总余额
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-balance">
                            ¥{{ "%.2f"|format(stats.total_balance if stats else 0) }}
                        </div>
                    </div>
                    <div class="text-info">
                        <i class="bi bi-wallet2 fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow stats-card h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            今日收入
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="today-consumption">
                            ¥{{ "%.2f"|format(stats.today_consumption if stats else 0) }}
                        </div>
                    </div>
                    <div class="text-warning">
                        <i class="bi bi-cash-coin fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row mb-4">
    <!-- 用户增长趋势 -->
    <div class="col-xl-8 col-lg-7 mb-4">
        <div class="card shadow">
            <div class="card-header card-header-admin">
                <h6 class="m-0 font-weight-bold">用户增长趋势</h6>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="userGrowthChart" width="400" height="160"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 服务使用统计 -->
    <div class="col-xl-4 col-lg-5 mb-4">
        <div class="card shadow">
            <div class="card-header card-header-admin">
                <h6 class="m-0 font-weight-bold">服务使用统计</h6>
            </div>
            <div class="card-body">
                <div class="chart-pie">
                    <canvas id="serviceUsageChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 近期活动 -->
<div class="row">
    <!-- 最新用户 -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header card-header-admin">
                <h6 class="m-0 font-weight-bold">最新注册用户</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>用户名</th>
                                <th>注册时间</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody id="recent-users">
                            <!-- 动态加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 系统状态 -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header card-header-admin">
                <h6 class="m-0 font-weight-bold">系统状态</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6 text-center">
                        <div class="border-end">
                            <div class="text-lg font-weight-bold text-success" id="system-uptime">99.9%</div>
                            <div class="text-xs text-muted">系统可用性</div>
                        </div>
                    </div>
                    <div class="col-6 text-center">
                        <div class="text-lg font-weight-bold text-info" id="response-time">120ms</div>
                        <div class="text-xs text-muted">平均响应时间</div>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-6 text-center">
                        <div class="border-end">
                            <div class="text-lg font-weight-bold text-primary" id="today-orders">
                                {{ stats.today_orders if stats else 0 }}
                            </div>
                            <div class="text-xs text-muted">今日订单</div>
                        </div>
                    </div>
                    <div class="col-6 text-center">
                        <div class="text-lg font-weight-bold text-warning" id="active-today">
                            {{ stats.active_today if stats else 0 }}
                        </div>
                        <div class="text-xs text-muted">今日活跃</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header card-header-admin">
                <h6 class="m-0 font-weight-bold">快速操作</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 col-sm-6 mb-3">
                        <button class="btn btn-admin w-100" onclick="location.href='{{ url_for('admin_users') }}'">
                            <i class="bi bi-people me-2"></i>
                            管理用户
                        </button>
                    </div>
                    <div class="col-md-3 col-sm-6 mb-3">
                        <button class="btn btn-outline-primary w-100" onclick="refreshStats()">
                            <i class="bi bi-arrow-clockwise me-2"></i>
                            刷新数据
                        </button>
                    </div>
                    <div class="col-md-3 col-sm-6 mb-3">
                        <button class="btn btn-outline-success w-100" onclick="exportData()">
                            <i class="bi bi-download me-2"></i>
                            导出数据
                        </button>
                    </div>
                    <div class="col-md-3 col-sm-6 mb-3">
                        <button class="btn btn-outline-info w-100" onclick="location.href='{{ url_for('admin_system') }}'">
                            <i class="bi bi-gear me-2"></i>
                            系统设置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
    loadRecentUsers();
    setInterval(refreshStats, 30000); // 30秒刷新一次
});

// 初始化图表
function initializeCharts() {
    // 用户增长趋势图
    const userGrowthCtx = document.getElementById('userGrowthChart').getContext('2d');
    new Chart(userGrowthCtx, {
        type: 'line',
        data: {
            labels: ['7天前', '6天前', '5天前', '4天前', '3天前', '2天前', '昨天', '今天'],
            datasets: [{
                label: '新增用户',
                data: [12, 19, 3, 5, 2, 3, 9, 15],
                borderColor: 'rgb(75, 192, 192)',
                tension: 0.1,
                fill: true,
                backgroundColor: 'rgba(75, 192, 192, 0.1)'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // 服务使用统计饼图
    const serviceUsageCtx = document.getElementById('serviceUsageChart').getContext('2d');
    new Chart(serviceUsageCtx, {
        type: 'doughnut',
        data: {
            labels: ['SQL转ER图', '测试用例生成', '答辩问题生成', '论文结构生成'],
            datasets: [{
                data: [45, 25, 20, 10],
                backgroundColor: [
                    '#4e73df',
                    '#1cc88a',
                    '#36b9cc',
                    '#f6c23e'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// 加载最新用户
async function loadRecentUsers() {
    try {
        const response = await apiRequest('/api/admin/recent-users');
        if (response.success) {
            const tbody = document.getElementById('recent-users');
            tbody.innerHTML = response.users.map(user => `
                <tr>
                    <td>${user.username}</td>
                    <td>${formatDate(user.created_at)}</td>
                    <td>
                        <span class="badge ${user.status == 1 ? 'bg-success' : 'bg-danger'} status-badge">
                            ${user.status == 1 ? '正常' : '禁用'}
                        </span>
                    </td>
                </tr>
            `).join('');
        }
    } catch (error) {
        console.error('加载最新用户失败:', error);
    }
}

// 刷新统计数据
async function refreshStats() {
    try {
        const response = await apiRequest('/api/admin/stats');
        if (response.success) {
            const stats = response.stats;
            document.getElementById('total-users').textContent = formatNumber(stats.total_users);
            document.getElementById('active-users').textContent = formatNumber(stats.active_users);
            document.getElementById('total-balance').textContent = formatCurrency(stats.total_balance);
            document.getElementById('today-consumption').textContent = formatCurrency(stats.today_consumption);
            document.getElementById('today-orders').textContent = formatNumber(stats.today_orders);
            document.getElementById('active-today').textContent = formatNumber(stats.active_today);
            
            // 显示成功消息
            showAlert('数据已刷新', 'success');
        }
    } catch (error) {
        console.error('刷新统计数据失败:', error);
        showAlert('刷新失败，请重试', 'danger');
    }
}

// 导出数据
function exportData() {
    const link = document.createElement('a');
    link.href = '/api/admin/export-data';
    link.download = `系统数据_${new Date().toISOString().split('T')[0]}.xlsx`;
    link.click();
}

// 显示警告消息
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // 3秒后自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}
</script>
{% endblock %}