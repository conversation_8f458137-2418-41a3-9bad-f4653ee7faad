{% extends "admin_base.html" %}

{% block page_title %}用户管理{% endblock %}

{% block content %}
<!-- 搜索和操作栏 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header card-header-admin">
                <div class="d-flex align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold">用户管理</h6>
                    <div class="d-flex gap-2">
                        <button class="btn btn-sm btn-success" onclick="showAddBalanceModal()">
                            <i class="bi bi-plus-circle me-1"></i>
                            批量充值
                        </button>
                        <button class="btn btn-sm btn-info" onclick="exportUsers()">
                            <i class="bi bi-download me-1"></i>
                            导出用户
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="搜索用户名或邀请码" id="searchInput">
                            <button class="btn btn-outline-secondary" type="button" onclick="searchUsers()">
                                <i class="bi bi-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="statusFilter" onchange="searchUsers()">
                            <option value="">全部状态</option>
                            <option value="1">正常</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="roleFilter" onchange="searchUsers()">
                            <option value="">全部角色</option>
                            <option value="user">普通用户</option>
                            <option value="admin">管理员</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="sortBy" onchange="searchUsers()">
                            <option value="created_at">注册时间</option>
                            <option value="last_login_at">最后登录</option>
                            <option value="balance">余额</option>
                            <option value="total_consumption">消费额</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-primary w-100" onclick="resetFilters()">
                            <i class="bi bi-arrow-clockwise me-1"></i>
                            重置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 用户列表 -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>
                                    <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                </th>
                                <th>用户ID</th>
                                <th>用户名</th>
                                <th>余额</th>
                                <th>累计充值</th>
                                <th>累计消费</th>
                                <th>邀请人数</th>
                                <th>注册时间</th>
                                <th>最后登录</th>
                                <th>状态</th>
                                <th>角色</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="userTableBody">
                            <!-- 动态加载用户数据 -->
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <nav aria-label="用户列表分页" class="mt-3">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- 动态生成分页 -->
                    </ul>
                </nav>
                
                <!-- 批量操作按钮 -->
                <div class="mt-3" id="batchActions" style="display: none;">
                    <div class="alert alert-info">
                        已选择 <span id="selectedCount">0</span> 个用户
                        <div class="btn-group ms-3">
                            <button class="btn btn-sm btn-success" onclick="batchAddBalance()">
                                批量充值
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="batchUpdateStatus(0)">
                                批量禁用
                            </button>
                            <button class="btn btn-sm btn-primary" onclick="batchUpdateStatus(1)">
                                批量启用
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 用户详情模态框 -->
<div class="modal fade" id="userDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">用户详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="userDetailContent">
                <!-- 动态加载用户详情 -->
            </div>
        </div>
    </div>
</div>

<!-- 充值模态框 -->
<div class="modal fade" id="addBalanceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">用户充值</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addBalanceForm">
                    <div class="mb-3">
                        <label for="targetUserId" class="form-label">目标用户</label>
                        <input type="text" class="form-control" id="targetUserId" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="addAmount" class="form-label">充值金额</label>
                        <input type="number" class="form-control" id="addAmount" step="0.01" min="0.01" required>
                    </div>
                    <div class="mb-3">
                        <label for="addReason" class="form-label">充值说明</label>
                        <textarea class="form-control" id="addReason" rows="2" placeholder="请输入充值原因"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" onclick="confirmAddBalance()">确认充值</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentPage = 1;
let totalPages = 1;
let selectedUsers = new Set();

document.addEventListener('DOMContentLoaded', function() {
    loadUsers();
    
    // 搜索框回车事件
    document.getElementById('searchInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchUsers();
        }
    });
});

// 加载用户列表
async function loadUsers(page = 1) {
    try {
        const searchTerm = document.getElementById('searchInput').value;
        const status = document.getElementById('statusFilter').value;
        const role = document.getElementById('roleFilter').value;
        const sortBy = document.getElementById('sortBy').value;
        
        const params = new URLSearchParams({
            page: page,
            per_page: 20,
            search: searchTerm,
            status: status,
            role: role,
            sort_by: sortBy
        });
        
        const response = await apiRequest(`/api/admin/users?${params}`);
        
        if (response.success) {
            displayUsers(response.data.users);
            updatePagination(response.data);
            currentPage = page;
            totalPages = response.data.pages;
        } else {
            showAlert(response.message || '加载用户列表失败', 'danger');
        }
    } catch (error) {
        console.error('加载用户列表失败:', error);
        showAlert('网络错误，请重试', 'danger');
    }
}

// 显示用户列表
function displayUsers(users) {
    const tbody = document.getElementById('userTableBody');
    tbody.innerHTML = users.map(user => `
        <tr>
            <td>
                <input type="checkbox" class="user-checkbox" value="${user.id}" 
                       onchange="updateSelectedUsers()">
            </td>
            <td>${user.id}</td>
            <td>
                <div class="d-flex align-items-center">
                    <strong>${user.username}</strong>
                    ${user.role === 'admin' ? '<span class="badge bg-danger ms-2">管理员</span>' : ''}
                </div>
            </td>
            <td class="text-end">¥${parseFloat(user.balance).toFixed(2)}</td>
            <td class="text-end">¥${parseFloat(user.total_recharge).toFixed(2)}</td>
            <td class="text-end">¥${parseFloat(user.total_consumption).toFixed(2)}</td>
            <td class="text-center">${user.invite_count || 0}</td>
            <td>${formatDate(user.created_at)}</td>
            <td>${user.last_login_at ? formatDate(user.last_login_at) : '从未登录'}</td>
            <td>
                <span class="badge ${user.status == 1 ? 'bg-success' : 'bg-danger'} status-badge">
                    ${user.status == 1 ? '正常' : '禁用'}
                </span>
            </td>
            <td>
                <span class="badge ${user.role === 'admin' ? 'bg-danger' : 'bg-primary'} status-badge">
                    ${user.role === 'admin' ? '管理员' : '用户'}
                </span>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="showUserDetail(${user.id})" title="查看详情">
                        <i class="bi bi-eye"></i>
                    </button>
                    <button class="btn btn-outline-success" onclick="showAddBalanceModal(${user.id}, '${user.username}')" title="充值">
                        <i class="bi bi-plus-circle"></i>
                    </button>
                    <button class="btn btn-outline-${user.status == 1 ? 'warning' : 'primary'}" 
                            onclick="toggleUserStatus(${user.id}, ${user.status == 1 ? 0 : 1})" 
                            title="${user.status == 1 ? '禁用' : '启用'}">
                        <i class="bi bi-${user.status == 1 ? 'ban' : 'check-circle'}"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// 更新分页
function updatePagination(data) {
    const pagination = document.getElementById('pagination');
    const { page, pages, total } = data;
    
    let paginationHTML = '';
    
    // 上一页
    if (page > 1) {
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadUsers(${page - 1})">上一页</a></li>`;
    }
    
    // 页码
    const startPage = Math.max(1, page - 2);
    const endPage = Math.min(pages, page + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `<li class="page-item ${i === page ? 'active' : ''}">
            <a class="page-link" href="#" onclick="loadUsers(${i})">${i}</a>
        </li>`;
    }
    
    // 下一页
    if (page < pages) {
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadUsers(${page + 1})">下一页</a></li>`;
    }
    
    pagination.innerHTML = paginationHTML;
}

// 搜索用户
function searchUsers() {
    currentPage = 1;
    loadUsers(1);
}

// 重置筛选器
function resetFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('roleFilter').value = '';
    document.getElementById('sortBy').value = 'created_at';
    loadUsers(1);
}

// 切换用户状态
async function toggleUserStatus(userId, newStatus) {
    const action = newStatus == 1 ? '启用' : '禁用';
    if (!confirm(`确定要${action}此用户吗？`)) return;
    
    try {
        const response = await apiRequest('/api/admin/user-status', 'POST', {
            user_id: userId,
            status: newStatus
        });
        
        if (response.success) {
            showAlert(`用户${action}成功`, 'success');
            loadUsers(currentPage);
        } else {
            showAlert(response.message || `${action}用户失败`, 'danger');
        }
    } catch (error) {
        console.error(`${action}用户失败:`, error);
        showAlert('网络错误，请重试', 'danger');
    }
}

// 显示用户详情
async function showUserDetail(userId) {
    try {
        const response = await apiRequest(`/api/admin/user-detail/${userId}`);
        
        if (response.success) {
            const user = response.user;
            const content = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>基本信息</h6>
                        <table class="table table-sm">
                            <tr><td>用户ID:</td><td>${user.id}</td></tr>
                            <tr><td>用户名:</td><td>${user.username}</td></tr>
                            <tr><td>邀请码:</td><td>${user.invite_code}</td></tr>
                            <tr><td>注册时间:</td><td>${formatDate(user.created_at)}</td></tr>
                            <tr><td>最后登录:</td><td>${user.last_login_at ? formatDate(user.last_login_at) : '从未登录'}</td></tr>
                            <tr><td>登录IP:</td><td>${user.last_login_ip || '未知'}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>财务信息</h6>
                        <table class="table table-sm">
                            <tr><td>当前余额:</td><td class="text-success">¥${parseFloat(user.balance).toFixed(2)}</td></tr>
                            <tr><td>累计充值:</td><td>¥${parseFloat(user.total_recharge).toFixed(2)}</td></tr>
                            <tr><td>累计消费:</td><td>¥${parseFloat(user.total_consumption).toFixed(2)}</td></tr>
                            <tr><td>邀请收益:</td><td>¥${parseFloat(user.invite_earnings).toFixed(2)}</td></tr>
                            <tr><td>邀请人数:</td><td>${user.invite_count || 0}</td></tr>
                        </table>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>最近消费记录</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>服务类型</th>
                                        <th>消费金额</th>
                                        <th>描述</th>
                                        <th>时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${user.recent_records ? user.recent_records.map(record => `
                                        <tr>
                                            <td>${record.service_type}</td>
                                            <td>¥${parseFloat(record.amount).toFixed(2)}</td>
                                            <td>${record.description || '-'}</td>
                                            <td>${formatDate(record.created_at)}</td>
                                        </tr>
                                    `).join('') : '<tr><td colspan="4" class="text-center">暂无记录</td></tr>'}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;
            
            document.getElementById('userDetailContent').innerHTML = content;
            new bootstrap.Modal(document.getElementById('userDetailModal')).show();
        } else {
            showAlert(response.message || '获取用户详情失败', 'danger');
        }
    } catch (error) {
        console.error('获取用户详情失败:', error);
        showAlert('网络错误，请重试', 'danger');
    }
}

// 显示充值模态框
function showAddBalanceModal(userId = null, username = '') {
    if (userId) {
        document.getElementById('targetUserId').value = `${userId} - ${username}`;
        document.getElementById('targetUserId').dataset.userId = userId;
    } else {
        document.getElementById('targetUserId').value = '批量充值';
        document.getElementById('targetUserId').dataset.userId = '';
    }
    
    document.getElementById('addAmount').value = '';
    document.getElementById('addReason').value = '';
    
    new bootstrap.Modal(document.getElementById('addBalanceModal')).show();
}

// 确认充值
async function confirmAddBalance() {
    const userId = document.getElementById('targetUserId').dataset.userId;
    const amount = parseFloat(document.getElementById('addAmount').value);
    const reason = document.getElementById('addReason').value;
    
    if (!amount || amount <= 0) {
        showAlert('请输入有效的充值金额', 'warning');
        return;
    }
    
    try {
        const url = userId ? '/api/admin/add-balance' : '/api/admin/batch-add-balance';
        const data = userId ? 
            { user_id: parseInt(userId), amount, description: reason } :
            { user_ids: Array.from(selectedUsers), amount, description: reason };
        
        const response = await apiRequest(url, 'POST', data);
        
        if (response.success) {
            showAlert('充值成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('addBalanceModal')).hide();
            loadUsers(currentPage);
        } else {
            showAlert(response.message || '充值失败', 'danger');
        }
    } catch (error) {
        console.error('充值失败:', error);
        showAlert('网络错误，请重试', 'danger');
    }
}

// 全选/取消全选
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.user-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateSelectedUsers();
}

// 更新选中用户
function updateSelectedUsers() {
    const checkboxes = document.querySelectorAll('.user-checkbox:checked');
    selectedUsers.clear();
    
    checkboxes.forEach(checkbox => {
        selectedUsers.add(parseInt(checkbox.value));
    });
    
    const batchActions = document.getElementById('batchActions');
    const selectedCount = document.getElementById('selectedCount');
    
    if (selectedUsers.size > 0) {
        batchActions.style.display = 'block';
        selectedCount.textContent = selectedUsers.size;
    } else {
        batchActions.style.display = 'none';
    }
}

// 批量操作
async function batchUpdateStatus(status) {
    if (selectedUsers.size === 0) {
        showAlert('请先选择用户', 'warning');
        return;
    }
    
    const action = status == 1 ? '启用' : '禁用';
    if (!confirm(`确定要${action}选中的 ${selectedUsers.size} 个用户吗？`)) return;
    
    try {
        const response = await apiRequest('/api/admin/batch-update-status', 'POST', {
            user_ids: Array.from(selectedUsers),
            status: status
        });
        
        if (response.success) {
            showAlert(`批量${action}成功`, 'success');
            selectedUsers.clear();
            document.getElementById('selectAll').checked = false;
            updateSelectedUsers();
            loadUsers(currentPage);
        } else {
            showAlert(response.message || `批量${action}失败`, 'danger');
        }
    } catch (error) {
        console.error(`批量${action}失败:`, error);
        showAlert('网络错误，请重试', 'danger');
    }
}

// 批量充值
function batchAddBalance() {
    if (selectedUsers.size === 0) {
        showAlert('请先选择用户', 'warning');
        return;
    }
    showAddBalanceModal();
}

// 导出用户数据
function exportUsers() {
    const link = document.createElement('a');
    link.href = '/api/admin/export-users';
    link.download = `用户数据_${new Date().toISOString().split('T')[0]}.xlsx`;
    link.click();
}

// 显示警告消息（复用仪表盘的函数）
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}
</script>
{% endblock %}