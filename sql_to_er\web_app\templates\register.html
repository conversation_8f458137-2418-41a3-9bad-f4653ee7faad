<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册 - 在线工具集</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f5f7fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }

        .register-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 450px;
            padding: 40px;
        }

        .register-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .register-header h2 {
            color: #2c3e50;
            font-size: 24px;
            font-weight: 600;
            margin: 0 0 8px 0;
        }

        .register-header p {
            color: #7f8c8d;
            font-size: 14px;
            margin: 0;
        }
        
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            color: #2c3e50;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 6px;
        }

        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }

        .form-control:focus {
            outline: none;
            border-color: #27ae60;
            box-shadow: 0 0 0 3px rgba(39, 174, 96, 0.1);
        }

        .btn-register {
            width: 100%;
            background: #27ae60;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .btn-register:hover {
            background: #229954;
        }

        .btn-register:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }

        .invite-info {
            background: #e8f5e8;
            border: 1px solid #c3e6cb;
            border-radius: 6px;
            padding: 16px;
            margin-bottom: 24px;
        }

        .invite-info h6 {
            color: #27ae60;
            font-size: 14px;
            font-weight: 600;
            margin: 0 0 8px 0;
        }

        .invite-info p {
            margin: 0;
            font-size: 13px;
            color: #5a6c57;
            line-height: 1.4;
        }
        
        .divider {
            text-align: center;
            margin: 24px 0;
            position: relative;
            color: #7f8c8d;
            font-size: 14px;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #ecf0f1;
        }

        .divider span {
            background: white;
            padding: 0 16px;
        }

        .login-link {
            text-align: center;
        }

        .login-link a {
            color: #27ae60;
            text-decoration: none;
            font-size: 14px;
        }

        .login-link a:hover {
            text-decoration: underline;
        }

        .back-home {
            position: fixed;
            top: 20px;
            left: 20px;
            color: #7f8c8d;
            text-decoration: none;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .back-home:hover {
            color: #2c3e50;
            text-decoration: none;
        }

        .alert {
            padding: 12px 16px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            display: none;
        }

        .loading i {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .password-strength {
            margin-top: 6px;
            font-size: 12px;
        }

        .strength-weak { color: #e74c3c; }
        .strength-medium { color: #f39c12; }
        .strength-strong { color: #27ae60; }
    </style>
</head>
<body>
    <a href="/" class="back-home">
        <i class="fas fa-arrow-left"></i> 返回首页
    </a>

    <div class="register-container">
        <div class="register-header">
            <h2>用户注册</h2>
            <p>注册即送10元体验额度</p>
        </div>

        <div class="invite-info">
            <h6>🎁 邀请奖励</h6>
            <p>• 注册即送10元体验额度<br>• 通过邀请码注册，邀请人可获得5元奖励</p>
        </div>

        <div id="alertContainer"></div>

        <form id="registerForm">
            <div class="form-group">
                <label for="username" class="form-label">用户名 *</label>
                <input type="text" class="form-control" id="username" placeholder="3-20个字符，支持字母数字下划线" required>
            </div>

            <div class="form-group">
                <label for="password" class="form-label">密码 *</label>
                <input type="password" class="form-control" id="password" placeholder="至少6个字符" required>
                <div id="passwordStrength" class="password-strength"></div>
            </div>

            <div class="form-group">
                <label for="confirmPassword" class="form-label">确认密码 *</label>
                <input type="password" class="form-control" id="confirmPassword" placeholder="请再次输入密码" required>
            </div>

            <div class="form-group">
                <label for="inviteCode" class="form-label">邀请码（可选）</label>
                <input type="text" class="form-control" id="inviteCode" placeholder="如有邀请码请填写">
            </div>

            <button type="submit" class="btn-register" id="registerBtn">
                <span class="normal-text">立即注册</span>
                <span class="loading">
                    <i class="fas fa-spinner"></i> 注册中...
                </span>
            </button>
        </form>

        <div class="divider">
            <span>已有账号？</span>
        </div>

        <div class="login-link">
            <a href="/login">立即登录</a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 密码强度检测
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strengthDiv = document.getElementById('passwordStrength');
            
            if (password.length === 0) {
                strengthDiv.innerHTML = '';
                return;
            }
            
            let strength = 0;
            if (password.length >= 6) strength++;
            if (password.match(/[a-z]/)) strength++;
            if (password.match(/[A-Z]/)) strength++;
            if (password.match(/[0-9]/)) strength++;
            if (password.match(/[^a-zA-Z0-9]/)) strength++;
            
            if (strength <= 2) {
                strengthDiv.innerHTML = '<span class="strength-weak">密码强度：弱</span>';
            } else if (strength <= 3) {
                strengthDiv.innerHTML = '<span class="strength-medium">密码强度：中</span>';
            } else {
                strengthDiv.innerHTML = '<span class="strength-strong">密码强度：强</span>';
            }
        });
        
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            const confirmPassword = document.getElementById('confirmPassword').value.trim();
            const inviteCode = document.getElementById('inviteCode').value.trim();
            const registerBtn = document.getElementById('registerBtn');
            
            // 表单验证
            if (!username || !password || !confirmPassword) {
                showAlert('请填写完整的注册信息', 'danger');
                return;
            }
            
            if (username.length < 3 || username.length > 20) {
                showAlert('用户名长度应在3-20个字符之间', 'danger');
                return;
            }
            
            if (password.length < 6) {
                showAlert('密码长度不能少于6个字符', 'danger');
                return;
            }
            
            if (password !== confirmPassword) {
                showAlert('两次输入的密码不一致', 'danger');
                return;
            }
            
            // 显示加载状态
            registerBtn.disabled = true;
            registerBtn.querySelector('.normal-text').style.display = 'none';
            registerBtn.querySelector('.loading').style.display = 'inline';
            
            try {
                const response = await fetch('/api/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password,
                        invite_code: inviteCode
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert(`注册成功！您的邀请码是：${result.invite_code}，已获得${result.bonus}元体验额度`, 'success');
                    setTimeout(() => {
                        window.location.href = '/login';
                    }, 2000);
                } else {
                    showAlert(result.message, 'danger');
                }
            } catch (error) {
                showAlert('注册失败，请稍后重试', 'danger');
            } finally {
                // 恢复按钮状态
                registerBtn.disabled = false;
                registerBtn.querySelector('.normal-text').style.display = 'inline';
                registerBtn.querySelector('.loading').style.display = 'none';
            }
        });
        
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            alertContainer.innerHTML = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }
    </script>
</body>
</html>
