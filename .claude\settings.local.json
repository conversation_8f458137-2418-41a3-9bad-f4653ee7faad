{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(ls:*)", "<PERSON><PERSON>(chmod:*)", "Bash(tree:*)", "Bash(find:*)", "Bash(pip install:*)", "Bash(apt-get:*)", "Bash(apt-get install:*)", "<PERSON><PERSON>(python:*)", "Bash(/root/sql4/sql_to_er/restart_web.sh:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(curl:*)", "Bash(/root/sql4/sql_to_er/restart_debug.sh:*)", "Bash(grep:*)", "Bash(pip3 list:*)", "Bash(pip3 install:*)", "<PERSON><PERSON>(true)", "Bash(rm:*)", "Bash(cp:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(hostname:*)", "Bash(kill:*)", "Bash(/root/sql4/sql_to_er/start_app.sh:*)", "Bash(bash:*)", "<PERSON><PERSON>(sed:*)", "Bash(sudo service:*)", "Bash(sudo systemctl start:*)", "<PERSON><PERSON>(mysql:*)"], "deny": []}}