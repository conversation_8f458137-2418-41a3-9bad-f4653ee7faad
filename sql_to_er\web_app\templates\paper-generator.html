<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI论文生成器 - 学术写作助手</title>
    <!-- 防止浏览器缓存 -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    
    <!-- 引入Quill富文本编辑器 -->
    <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
    <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
    
    <!-- 引入Font Awesome图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #2c3e50;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
        }

        /* 头部样式 */
        .header {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(20px);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #fff, #e3f2fd);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        /* 导航栏 */
        .navbar {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(20px);
            padding: 15px 30px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .navbar a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }

        .navbar a:hover {
            background: rgba(255,255,255,0.2);
        }

        .home-link {
            font-weight: bold;
            font-size: 1.1rem;
        }

        /* 主要内容区域 */
        .main-content {
            display: grid;
            grid-template-columns: 480px 1fr;
            gap: 30px;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        /* 左侧配置面板 */
        .config-panel {
            background: #f8fafc;
            padding: 30px;
            border-right: 1px solid #e8ecf0;
        }

        .panel-title {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 25px;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #374151;
            font-size: 14px;
        }

        .required {
            color: #ef4444;
        }

        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-control:invalid {
            border-color: #ef4444;
        }

        textarea.form-control {
            resize: vertical;
            min-height: 120px;
        }

        /* 按钮样式 */
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            justify-content: center;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            width: 100%;
            padding: 15px;
            font-size: 16px;
            margin-top: 20px;
        }

        .btn-primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }

        .btn-outline {
            background: transparent;
            border: 2px solid #e5e7eb;
            color: #6b7280;
        }

        .btn-outline:hover {
            border-color: #667eea;
            color: #667eea;
        }

        /* 右侧编辑区域 */
        .editor-section {
            padding: 30px;
            display: flex;
            flex-direction: column;
            height: 85vh;
        }

        .editor-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e5e7eb;
        }

        .editor-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .editor-actions {
            display: flex;
            gap: 10px;
        }

        .editor-container {
            flex: 1;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
        }

        /* Quill编辑器自定义样式 */
        .ql-toolbar {
            border-bottom: 1px solid #e5e7eb;
            background: #f9fafb;
        }

        .ql-editor {
            min-height: 600px;
            font-size: 14px;
            line-height: 1.8;
            font-family: 'Times New Roman', '宋体', serif;
        }

        /* 进度指示器 */
        .progress-container {
            margin-top: 15px;
            display: none;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .progress-title {
            font-weight: 600;
            color: #374151;
            font-size: 14px;
        }

        .progress-percentage {
            font-weight: 600;
            color: #667eea;
            font-size: 14px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 12px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s ease;
            width: 0%;
            border-radius: 4px;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: progress-shimmer 2s infinite;
        }

        @keyframes progress-shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-text {
            text-align: center;
            font-size: 13px;
            color: #6b7280;
            margin-bottom: 8px;
        }

        .progress-sections {
            border-top: 1px solid #e5e7eb;
            padding-top: 12px;
            margin-top: 8px;
        }

        .section-progress {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 6px;
        }

        .current-section {
            font-size: 12px;
            color: #059669;
            font-weight: 500;
            text-align: center;
            padding: 4px 8px;
            background: #f0fdf4;
            border-radius: 4px;
            border-left: 3px solid #10b981;
        }

        /* 加载遮罩 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.9);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid #e5e7eb;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 提示信息 */
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #10b981;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast.error {
            background: #ef4444;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .config-panel {
                border-right: none;
                border-bottom: 1px solid #e8ecf0;
            }
            
            .editor-section {
                height: 60vh;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .config-panel, .editor-section {
                padding: 20px;
            }
        }

        /* 字段验证样式 */
        .field-error {
            border-color: #ef4444 !important;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
        }

        .error-message {
            color: #ef4444;
            font-size: 12px;
            margin-top: 5px;
            display: none;
        }

        .field-error + .error-message {
            display: block;
        }
    </style>
</head>
<body>
    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
    </div>

    <div class="container">
        <!-- 导航栏 -->
        <nav class="navbar">
            <a href="/" class="home-link">
                <i class="fas fa-home"></i> 返回首页
            </a>
            <div>
                <span style="color: white; opacity: 0.8;">AI论文生成器</span>
            </div>
        </nav>

        <!-- 头部 -->
        <header class="header">
            <h1><i class="fas fa-graduation-cap"></i> AI论文生成器</h1>
            <p>基于人工智能的学术论文写作助手，让学术写作更简单高效</p>
        </header>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 左侧配置面板 -->
            <div class="config-panel">
                <h3 class="panel-title">
                    <i class="fas fa-cog"></i>
                    论文配置
                </h3>
                
                <div class="form-group">
                    <label class="form-label">
                        论文题目 <span class="required">*</span>
                    </label>
                    <input 
                        type="text" 
                        id="paperTitle" 
                        class="form-control" 
                        placeholder="请输入论文题目"
                        maxlength="100"
                        required
                    >
                    <div class="error-message">请输入论文题目</div>
                </div>

                <div class="form-group">
                    <label class="form-label">
                        研究领域 <span class="required">*</span>
                    </label>
                    <select id="researchField" class="form-control" required>
                        <option value="">请选择研究领域</option>
                        <option value="计算机科学">计算机科学</option>
                        <option value="人工智能">人工智能</option>
                        <option value="软件工程">软件工程</option>
                        <option value="数据科学">数据科学</option>
                        <option value="网络安全">网络安全</option>
                        <option value="机器学习">机器学习</option>
                        <option value="物联网">物联网</option>
                        <option value="区块链">区块链</option>
                        <option value="云计算">云计算</option>
                        <option value="大数据">大数据</option>
                    </select>
                    <div class="error-message">请选择研究领域</div>
                </div>

                <div class="form-group">
                    <label class="form-label">论文类型</label>
                    <select id="paperType" class="form-control">
                        <option value="本科毕业论文">本科毕业论文</option>
                        <option value="硕士学位论文">硕士学位论文</option>
                        <option value="学术论文">学术论文</option>
                        <option value="研究报告">研究报告</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">目标字数</label>
                    <select id="targetWords" class="form-control">
                        <option value="8000">8000字（本科短篇）</option>
                        <option value="12000" selected>12000字（本科标准）</option>
                        <option value="15000">15000字（本科长篇）</option>
                        <option value="20000">20000字（硕士短篇）</option>
                        <option value="30000">30000字（硕士标准）</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">论文摘要</label>
                    <textarea
                        id="paperAbstract"
                        class="form-control"
                        rows="6"
                        placeholder="请输入论文摘要（可选，有助于生成更准确的内容）"
                        maxlength="800"
                    ></textarea>
                </div>

                <div class="form-group">
                    <label class="form-label">关键词</label>
                    <input 
                        type="text" 
                        id="keywords" 
                        class="form-control" 
                        placeholder="请输入3-5个关键词，用逗号分隔"
                        maxlength="100"
                    >
                </div>

                <button type="button" class="btn btn-primary" id="generateBtn">
                    <i class="fas fa-magic"></i>
                    生成论文
                </button>

                <!-- 进度条 -->
                <div class="progress-container" id="progressContainer">
                    <div class="progress-header">
                        <span class="progress-title">生成进度</span>
                        <span class="progress-percentage" id="progressPercentage">0%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">准备生成...</div>
                    <div class="progress-sections" id="progressSections" style="display: none;">
                        <div class="section-progress">
                            <span>章节进度:</span>
                            <span id="sectionCount">0/0</span>
                        </div>
                        <div class="current-section" id="currentSection">等待开始...</div>
                    </div>
                </div>
            </div>

            <!-- 右侧编辑器 -->
            <div class="editor-section">
                <div class="editor-header">
                    <div class="editor-title">
                        <i class="fas fa-edit"></i>
                        论文编辑器
                    </div>
                    <div class="editor-actions">
                        <button class="btn btn-outline" onclick="saveDraft()">
                            <i class="fas fa-save"></i> 保存草稿
                        </button>
                        <button class="btn btn-info" onclick="showMyPapers()">
                            <i class="fas fa-folder-open"></i> 我的论文
                        </button>
                        <button class="btn btn-success" onclick="exportToWord()">
                            <i class="fas fa-file-word"></i> 导出Word
                        </button>
                    </div>
                </div>
                <div class="editor-container">
                    <div id="editor"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let quill;
        let isGenerating = false;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== 页面加载完成，开始初始化 ===');
            console.log('版本信息: 已修复确认对话框问题 - 2025-07-28');
            initializeEditor();
            setupEventListeners();
            
            // 检查URL参数中是否有要加载的论文ID
            const urlParams = new URLSearchParams(window.location.search);
            const paperId = urlParams.get('paper_id');
            
            if (paperId) {
                console.log('检测到URL中的论文ID，加载论文:', paperId);
                // 延迟一点确保编辑器初始化完成
                setTimeout(() => {
                    loadPaperToEditor(paperId);
                }, 500);
            } else {
                // 检查是否有之前保存的论文ID
                const savedPaperId = localStorage.getItem('currentPaperId');
                if (savedPaperId) {
                    console.log('检测到本地保存的论文ID，询问是否恢复:', savedPaperId);
                    if (confirm('检测到之前保存的论文，是否恢复？')) {
                        setTimeout(() => {
                            loadPaperToEditor(savedPaperId);
                        }, 500);
                    } else {
                        // 如果不恢复，加载本地草稿
                        loadDraft();
                    }
                } else {
                    // 加载本地草稿
                    loadDraft();
                }
            }
        });

        // 初始化Quill编辑器
        function initializeEditor() {
            console.log('初始化编辑器...');
            
            quill = new Quill('#editor', {
                theme: 'snow',
                modules: {
                    toolbar: [
                        [{ 'header': [1, 2, 3, false] }],
                        ['bold', 'italic', 'underline'],
                        [{ 'color': [] }, { 'background': [] }],
                        [{ 'align': [] }],
                        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                        ['blockquote', 'code-block'],
                        ['link'],
                        ['clean']
                    ]
                },
                placeholder: '论文内容将在这里生成...\n\n您也可以手动编辑和完善内容。',
            });

            // 设置默认内容
            quill.setContents([
                { insert: '论文标题', attributes: { header: 1, align: 'center' } },
                { insert: '\n\n' },
                { insert: '摘要', attributes: { header: 2 } },
                { insert: '\n这里是论文摘要内容...\n\n' },
                { insert: '关键词：', attributes: { bold: true } },
                { insert: '关键词1, 关键词2, 关键词3\n\n' },
                { insert: '1. 引言', attributes: { header: 2 } },
                { insert: '\n这里是引言内容...\n\n' }
            ]);

            console.log('编辑器初始化完成');
        }

        // 设置事件监听器
        function setupEventListeners() {
            console.log('设置事件监听器...');

            // 生成论文按钮
            const generateBtn = document.getElementById('generateBtn');
            if (generateBtn) {
                generateBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('点击生成论文按钮');
                    generatePaper();
                });
            }

            // 表单字段验证
            const titleInput = document.getElementById('paperTitle');
            const fieldSelect = document.getElementById('researchField');

            if (titleInput) {
                titleInput.addEventListener('input', function() {
                    clearFieldError(this);
                });
                titleInput.addEventListener('blur', function() {
                    validateField(this, '请输入论文题目');
                });
            }

            if (fieldSelect) {
                fieldSelect.addEventListener('change', function() {
                    clearFieldError(this);
                    validateField(this, '请选择研究领域');
                });
            }

            console.log('事件监听器设置完成');
        }

        // 字段验证
        function validateField(field, message) {
            const value = field.value.trim();
            if (!value) {
                showFieldError(field, message);
                return false;
            }
            return true;
        }

        // 显示字段错误
        function showFieldError(field, message) {
            field.classList.add('field-error');
            const errorElement = field.nextElementSibling;
            if (errorElement && errorElement.classList.contains('error-message')) {
                errorElement.textContent = message;
            }
        }

        // 清除字段错误
        function clearFieldError(field) {
            field.classList.remove('field-error');
        }

        // 生成论文主函数
        async function generatePaper() {
            console.log('=== 开始生成论文 ===');

            if (isGenerating) {
                console.log('正在生成中，忽略重复请求');
                return;
            }

            try {
                // 1. 获取表单数据
                const formData = getFormData();
                if (!formData) {
                    console.log('表单验证失败');
                    return;
                }

                console.log('表单数据:', formData);

                // 2. 开始生成
                isGenerating = true;
                setGenerateButtonState(true);
                showProgress(true);
                updateProgress(5, '正在初始化论文生成任务...');

                // 3. 启动论文生成任务
                const response = await fetch('/api/generate-paper', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                console.log('API响应状态:', response.status);

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.message || `请求失败 (${response.status})`);
                }

                const result = await response.json();
                console.log('API响应结果:', result);

                if (!result.success) {
                    throw new Error(result.message || '启动生成任务失败');
                }

                // 4. 开始轮询任务进度
                const taskId = result.task_id;
                console.log('任务ID:', taskId);
                updateProgress(10, '论文生成任务已启动，开始生成...');

                // 5. 轮询进度
                console.log('即将开始轮询进度...');
                await pollProgress(taskId);
                console.log('轮询进度完成');

            } catch (error) {
                console.error('生成论文失败:', error);
                showToast('生成失败: ' + error.message, 'error');
                updateProgress(0, '生成失败');
                isGenerating = false;
                setGenerateButtonState(false);
                setTimeout(() => {
                    showProgress(false);
                }, 2000);
            }
        }

        // 轮询任务进度
        async function pollProgress(taskId) {
            console.log('开始轮询任务进度, taskId:', taskId);
            const startTime = Date.now();
            let maxPollingTime = 1800000; // 最长轮询30分钟，增加到30分钟避免中断

            const poll = async () => {
                try {
                    console.log('发送进度查询请求...');
                    const response = await fetch(`/api/paper-progress/${taskId}`);
                    console.log('进度查询响应状态:', response.status);
                    
                    if (!response.ok) {
                        throw new Error(`查询进度失败 (${response.status})`);
                    }

                    const data = await response.json();
                    console.log('进度数据:', data);

                    if (!data.success) {
                        throw new Error(data.message || '查询进度失败');
                    }

                    // 更新进度显示
                    const progressText = buildProgressText(data);
                    updateProgress(data.progress, progressText, data);

                    // 估算剩余时间
                    const elapsed = Date.now() - startTime;
                    if (data.progress > 5) {
                        const estimatedTotal = elapsed / (data.progress / 100);
                        const remaining = Math.max(0, estimatedTotal - elapsed);
                        const remainingMinutes = Math.ceil(remaining / 60000);
                        console.log(`已用时: ${Math.ceil(elapsed/1000)}秒, 预计还需: ${remainingMinutes}分钟`);
                    }

                    // 检查是否完成
                    if (data.status === 'completed') {
                        console.log('任务完成');
                        
                        // 处理生成的内容 - 完全重写版本，确保内容一定能显示
                        if (data.content) {
                            try {
                                console.log('=== 开始插入内容到编辑器 ===');
                                console.log('原始内容长度:', data.content.length);
                                console.log('内容前200字符:', data.content.substring(0, 200));
                                
                                // 先清空编辑器
                                quill.setText('');
                                console.log('编辑器已清空');
                                
                                // 多重保险的内容插入策略
                                let insertSuccess = false;
                                
                                // 策略1：直接使用Quill的clipboard API
                                try {
                                    // 确保编辑器处于焦点状态
                                    quill.focus();
                                    
                                    // 使用Quill的dangerouslyPasteHTML方法
                                    quill.clipboard.dangerouslyPasteHTML(0, data.content);
                                    
                                    // 立即验证插入结果
                                    setTimeout(() => {
                                        const insertedLength = quill.getText().length;
                                        console.log('方法1插入后内容长度:', insertedLength);
                                        
                                        if (insertedLength > 50) {
                                            console.log('✅ 方法1成功：dangerouslyPasteHTML');
                                            insertSuccess = true;
                                            
                                            // 滚动到顶部
                                            quill.setSelection(0, 0);
                                            
                                        } else {
                                            console.warn('方法1失败：内容太短，尝试方法2');
                                            executeMethod2();
                                        }
                                    }, 200);
                                    
                                } catch (error) {
                                    console.error('方法1异常:', error);
                                    executeMethod2();
                                }
                                
                                // 策略2：使用innerHTML直接设置
                                function executeMethod2() {
                                    try {
                                        console.log('执行方法2：innerHTML直接设置');
                                        quill.root.innerHTML = data.content;
                                        
                                        setTimeout(() => {
                                            const insertedLength = quill.getText().length;
                                            console.log('方法2插入后内容长度:', insertedLength);
                                            
                                            if (insertedLength > 50) {
                                                console.log('✅ 方法2成功：innerHTML设置');
                                                insertSuccess = true;
                                                quill.setSelection(0, 0);
                                            } else {
                                                console.warn('方法2失败，尝试方法3');
                                                executeMethod3();
                                            }
                                        }, 200);
                                        
                                    } catch (error) {
                                        console.error('方法2异常:', error);
                                        executeMethod3();
                                    }
                                }
                                
                                // 策略3：转换为纯文本并使用setText
                                function executeMethod3() {
                                    try {
                                        console.log('执行方法3：纯文本设置');
                                        
                                        // 移除HTML标签但保留基本结构
                                        let cleanText = data.content
                                            .replace(/<h[1-6][^>]*>/gi, '\n\n【')
                                            .replace(/<\/h[1-6]>/gi, '】\n')
                                            .replace(/<p[^>]*>/gi, '\n')
                                            .replace(/<\/p>/gi, '\n')
                                            .replace(/<br[^>]*>/gi, '\n')
                                            .replace(/<[^>]*>/g, '')
                                            .replace(/\n\n+/g, '\n\n')
                                            .trim();
                                        
                                        quill.setText(cleanText);
                                        
                                        setTimeout(() => {
                                            const insertedLength = quill.getText().length;
                                            console.log('方法3插入后内容长度:', insertedLength);
                                            
                                            if (insertedLength > 50) {
                                                console.log('✅ 方法3成功：纯文本设置');
                                                insertSuccess = true;
                                                quill.setSelection(0, 0);
                                            } else {
                                                console.error('❌ 所有方法都失败了！');
                                                showToast('内容插入失败，请手动复制粘贴', 'error');
                                            }
                                        }, 200);
                                        
                                    } catch (error) {
                                        console.error('方法3异常:', error);
                                        showToast('内容插入完全失败，请刷新页面重试', 'error');
                                    }
                                }
                                
                                // 最终验证（3秒后）
                                setTimeout(() => {
                                    const finalLength = quill.getText().length;
                                    console.log('=== 最终验证结果 ===');
                                    console.log('编辑器最终内容长度:', finalLength);
                                    
                                    if (finalLength > 50) {
                                        console.log('✅ 内容插入最终确认成功');
                                        showToast('论文内容已成功加载到编辑器！', 'success');
                                    } else {
                                        console.error('❌ 内容插入最终确认失败');
                                        showToast('内容可能未正确插入，请检查编辑器', 'error');
                                    }
                                }, 3000);
                                
                            } catch (error) {
                                console.error('整个插入过程发生严重错误:', error);
                                showToast('内容插入失败，请刷新页面重试', 'error');
                            }
                        } else {
                            console.warn('生成完成但没有内容数据');
                            showToast('生成完成但内容为空，请重新生成', 'error');
                        }

                        updateProgress(100, '论文生成完成！正在优化显示效果...');
                        
                        // 延迟一点显示成功消息，给内容插入时间
                        setTimeout(() => {
                            const wordCount = estimateWordCount(data.content);
                            showToast(`🎉 论文生成成功！总字数约 ${wordCount} 字，内容已加载到编辑器`, 'success');
                        }, 2000);
                        
                        // 自动保存到数据库
                        setTimeout(() => {
                            savePaperToDatabase();
                        }, 1000);
                        
                        // 也保存草稿到本地
                        setTimeout(() => {
                            saveDraft();
                        }, 1500);

                        // 重置状态
                        isGenerating = false;
                        setGenerateButtonState(false);
                        setTimeout(() => {
                            showProgress(false);
                        }, 3000);
                        
                        return;
                    }

                    // 检查是否出错
                    if (data.status === 'error') {
                        throw new Error(data.error || '生成过程中出现错误');
                    }

                    // 检查超时 - 完全移除用户确认，自动延长等待时间
                    if (Date.now() - startTime > maxPollingTime) {
                        console.log('=== 检测到超时，自动延长等待时间 ===');
                        console.log('当前时间:', new Date().toLocaleString());
                        console.log('已用时间:', Math.round((Date.now() - startTime) / 1000), '秒');
                        console.log('【重要】不会弹出任何确认对话框，已完全移除！');
                        
                        // 自动延长等待时间，绝不询问用户
                        maxPollingTime = Date.now() - startTime + 900000; // 再等15分钟
                        
                        // 更新进度提示
                        updateProgress(data.progress || 50, '论文生成时间较长，系统自动延长等待时间，无需任何操作...');
                        console.log('已自动延长等待时间15分钟，继续轮询...');
                        
                        setTimeout(poll, 5000); // 5秒后继续轮询
                        return;
                    }

                    // 继续轮询
                    if (data.status === 'running') {
                        setTimeout(poll, 3000); // 每3秒查询一次，减少服务器压力
                    }

                } catch (error) {
                    console.error('轮询进度失败:', error);
                    showToast('生成失败: ' + error.message, 'error');
                    updateProgress(0, '生成失败');
                    
                    // 重置状态
                    isGenerating = false;
                    setGenerateButtonState(false);
                    setTimeout(() => {
                        showProgress(false);
                    }, 2000);
                }
            };

            // 开始轮询
            poll();
        }

        // 构建进度文本 - 更新版本
        function buildProgressText(data) {
            let text = data.message || '正在生成...';
            return text; // 简化文本，详细信息在章节显示区域
        }

        // 估算字数
        function estimateWordCount(htmlContent) {
            if (!htmlContent) return 0;
            
            // 移除HTML标签，估算字数
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = htmlContent;
            const textContent = tempDiv.textContent || tempDiv.innerText || '';
            
            // 中文字符计数
            const chineseCount = (textContent.match(/[\u4e00-\u9fa5]/g) || []).length;
            // 英文单词计数
            const englishWords = textContent.replace(/[\u4e00-\u9fa5]/g, '').trim().split(/\s+/).filter(word => word.length > 0).length;
            
            return chineseCount + englishWords;
        }

        // 获取表单数据
        function getFormData() {
            const title = document.getElementById('paperTitle').value.trim();
            const field = document.getElementById('researchField').value.trim();

            // 验证必填字段
            let hasError = false;

            if (!title) {
                showFieldError(document.getElementById('paperTitle'), '请输入论文题目');
                hasError = true;
            }

            if (!field) {
                showFieldError(document.getElementById('researchField'), '请选择研究领域');
                hasError = true;
            }

            if (hasError) {
                showToast('请填写所有必填字段', 'error');
                return null;
            }

            return {
                title: title,
                field: field,
                type: document.getElementById('paperType').value,
                words: parseInt(document.getElementById('targetWords').value),
                abstract: document.getElementById('paperAbstract').value.trim(),
                keywords: document.getElementById('keywords').value.trim(),
                requirements: ''
            };
        }

        // 保存论文到数据库
        async function savePaperToDatabase() {
            try {
                console.log('开始保存论文到数据库...');
                
                const content = quill.getContents();
                const title = document.getElementById('paperTitle').value || '未命名论文';
                const field = document.getElementById('researchField').value || '';
                const paperType = document.getElementById('paperType').value || '本科毕业论文';
                const targetWords = parseInt(document.getElementById('targetWords').value) || 12000;
                const abstract = document.getElementById('paperAbstract').value.trim() || '';
                const keywords = document.getElementById('keywords').value.trim() || '';
                
                const paperData = {
                    title: title,
                    field: field,
                    type: paperType,
                    target_words: targetWords,
                    abstract: abstract,
                    keywords: keywords,
                    content: content, // Quill Delta格式
                    html_content: quill.root.innerHTML, // HTML格式
                    created_at: new Date().toISOString(),
                    status: 'completed'
                };
                
                console.log('准备发送数据到服务器:', {
                    title: paperData.title,
                    content_length: JSON.stringify(content).length,
                    html_length: paperData.html_content.length
                });
                
                const response = await fetch('/api/save-paper', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(paperData)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    console.log('论文保存成功:', result);
                    if (result.success) {
                        showToast('论文已保存到数据库！论文ID: ' + result.paper_id);
                        
                        // 保存ID到本地，方便后续操作
                        localStorage.setItem('currentPaperId', result.paper_id);
                    } else {
                        showToast('保存失败: ' + result.message, 'error');
                    }
                } else {
                    console.error('保存请求失败:', response.status, response.statusText);
                    showToast('保存到数据库失败: HTTP ' + response.status, 'error');
                }
                
            } catch (error) {
                console.error('保存论文到数据库失败:', error);
                showToast('保存到数据库失败: ' + error.message, 'error');
            }
        }
        
        // 保存草稿
        function saveDraft() {
            try {
                const content = quill.getContents();
                const title = document.getElementById('paperTitle').value || '未命名论文';
                
                const draft = {
                    title: title,
                    content: content,
                    timestamp: new Date().toISOString()
                };
                
                localStorage.setItem('paperDraft', JSON.stringify(draft));
                showToast('草稿已保存');
                console.log('草稿已保存到本地存储');
            } catch (error) {
                console.error('保存草稿失败:', error);
                showToast('保存草稿失败', 'error');
            }
        }

        // 加载草稿
        function loadDraft() {
            try {
                const draft = localStorage.getItem('paperDraft');
                if (draft) {
                    const data = JSON.parse(draft);
                    
                    // 询问是否恢复草稿
                    if (confirm('发现之前保存的草稿，是否恢复？')) {
                        if (data.title) {
                            document.getElementById('paperTitle').value = data.title;
                        }
                        if (data.content) {
                            quill.setContents(data.content);
                        }
                        showToast('草稿已恢复');
                        console.log('草稿已恢复');
                    }
                }
            } catch (error) {
                console.error('加载草稿失败:', error);
            }
        }

        // 导出Word文档
        async function exportToWord() {
            try {
                const content = quill.getContents();
                const title = document.getElementById('paperTitle').value || '未命名论文';
                
                if (!content.ops || content.ops.length === 0) {
                    showToast('请先生成论文内容', 'error');
                    return;
                }

                showLoading(true);

                const response = await fetch('/api/export-paper-word', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        title: title,
                        content: content,
                        references: []
                    })
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `${title}_${new Date().toISOString().slice(0,10)}.docx`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                    
                    showToast('Word文档导出成功！');
                } else {
                    const errorData = await response.json();
                    throw new Error(errorData.error || '导出失败');
                }
            } catch (error) {
                console.error('导出失败:', error);
                showToast('导出失败: ' + error.message, 'error');
            } finally {
                showLoading(false);
            }
        }
        
        // 加载论文到编辑器
        async function loadPaperToEditor(paperId) {
            try {
                console.log('加载论文到编辑器，ID:', paperId);
                showLoading(true);
                
                const response = await fetch(`/api/paper/${paperId}`);
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success && result.paper) {
                        const paper = result.paper;
                        
                        // 填充表单字段
                        document.getElementById('paperTitle').value = paper.title || '';
                        document.getElementById('researchField').value = paper.field || '';
                        document.getElementById('paperType').value = paper.paper_type || '本科毕业论文';
                        document.getElementById('targetWords').value = paper.target_words || 12000;
                        document.getElementById('paperAbstract').value = paper.abstract || '';
                        document.getElementById('keywords').value = paper.keywords || '';
                        
                        // 加载内容到编辑器
                        if (paper.content && typeof paper.content === 'object') {
                            // 使用Quill Delta格式
                            quill.setContents(paper.content);
                            console.log('已使用Delta格式加载论文内容');
                        } else if (paper.html_content) {
                            // 使用HTML格式
                            try {
                                quill.clipboard.dangerouslyPasteHTML(paper.html_content);
                                console.log('已使用HTML格式加载论文内容');
                            } catch (error) {
                                console.error('HTML格式加载失败，尝试纯文本:', error);
                                const cleanText = paper.html_content.replace(/<[^>]*>/g, '');
                                quill.setText(cleanText);
                            }
                        }
                        
                        // 保存当前论文ID
                        localStorage.setItem('currentPaperId', paperId);
                        
                        showToast(`论文"${paper.title}"加载成功！`);
                        console.log('论文加载完成');
                        
                    } else {
                        showToast('论文不存在或加载失败', 'error');
                    }
                } else {
                    showToast('加载论文失败: HTTP ' + response.status, 'error');
                }
                
            } catch (error) {
                console.error('加载论文失败:', error);
                showToast('加载论文失败: ' + error.message, 'error');
            } finally {
                showLoading(false);
            }
        }
        
        // 显示我的论文列表
        async function showMyPapers() {
            try {
                const response = await fetch('/api/papers');
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success && result.data.papers.length > 0) {
                        let papersHtml = '<div style="max-height: 400px; overflow-y: auto;">';
                        papersHtml += '<h4>我的论文 (' + result.data.total + '篇)</h4>';
                        
                        result.data.papers.forEach(paper => {
                            const createdDate = new Date(paper.created_at).toLocaleString('zh-CN');
                            const contentLength = paper.content_length || 0;
                            papersHtml += `
                                <div style="border: 1px solid #ddd; margin: 10px 0; padding: 10px; border-radius: 5px;">
                                    <h5 style="margin: 0 0 5px 0; color: #333;">${paper.title}</h5>
                                    <p style="margin: 5px 0; color: #666; font-size: 0.9em;">
                                        <i class="fas fa-tag"></i> ${paper.field || '未指定领域'} | 
                                        <i class="fas fa-file-alt"></i> ${paper.paper_type || '本科毕业论文'} | 
                                        <i class="fas fa-text-width"></i> ${contentLength}字符
                                    </p>
                                    <p style="margin: 5px 0; color: #999; font-size: 0.8em;">
                                        <i class="fas fa-clock"></i> 创建时间: ${createdDate}
                                    </p>
                                    <button onclick="loadPaperToEditor(${paper.id})" class="btn btn-sm btn-primary" style="margin-right: 5px;">
                                        <i class="fas fa-edit"></i> 编辑
                                    </button>
                                </div>
                            `;
                        });
                        
                        papersHtml += '</div>';
                        
                        // 创建模态框显示论文列表
                        const modalHtml = `
                            <div id="papersModal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
                                <div style="background: white; padding: 20px; border-radius: 10px; max-width: 600px; width: 90%;">
                                    ${papersHtml}
                                    <div style="text-align: center; margin-top: 15px;">
                                        <button onclick="closePapersModal()" class="btn btn-secondary">关闭</button>
                                    </div>
                                </div>
                            </div>
                        `;
                        
                        document.body.insertAdjacentHTML('beforeend', modalHtml);
                        
                    } else {
                        showToast('暂无保存的论文，快去生成一篇吧！', 'info');
                    }
                } else {
                    showToast('获取论文列表失败', 'error');
                }
                
            } catch (error) {
                console.error('获取论文列表失败:', error);
                showToast('获取论文列表失败: ' + error.message, 'error');
            }
        }
        
        // 关闭论文列表模态框
        function closePapersModal() {
            const modal = document.getElementById('papersModal');
            if (modal) {
                modal.remove();
            }
        }

        // 显示/隐藏加载状态
        function showLoading(show) {
            const overlay = document.getElementById('loadingOverlay');
            if (overlay) {
                overlay.style.display = show ? 'flex' : 'none';
            }
        }

        // 显示/隐藏进度条
        function showProgress(show) {
            const container = document.getElementById('progressContainer');
            if (container) {
                container.style.display = show ? 'block' : 'none';
            }
        }

        // 更新进度
        function updateProgress(percentage, text, sectionData = null) {
            const fill = document.getElementById('progressFill');
            const textElement = document.getElementById('progressText');
            const percentageElement = document.getElementById('progressPercentage');
            const sectionsContainer = document.getElementById('progressSections');
            const sectionCountElement = document.getElementById('sectionCount');
            const currentSectionElement = document.getElementById('currentSection');
            
            if (fill) {
                fill.style.width = percentage + '%';
            }
            
            if (textElement) {
                textElement.textContent = text;
            }
            
            if (percentageElement) {
                percentageElement.textContent = Math.round(percentage) + '%';
            }
            
            // 更新章节信息
            if (sectionData && sectionsContainer) {
                sectionsContainer.style.display = 'block';
                
                if (sectionCountElement && sectionData.total_sections > 0) {
                    sectionCountElement.textContent = `${sectionData.sections_completed}/${sectionData.total_sections}`;
                }
                
                if (currentSectionElement && sectionData.current_section) {
                    currentSectionElement.textContent = `正在生成: ${sectionData.current_section}`;
                }
            } else if (sectionsContainer) {
                sectionsContainer.style.display = 'none';
            }
        }

        // 显示提示信息
        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            
            const icon = type === 'error' ? 'times-circle' : 'check-circle';
            toast.innerHTML = `
                <i class="fas fa-${icon}"></i>
                <span>${message}</span>
            `;
            
            document.body.appendChild(toast);
            
            // 显示动画
            setTimeout(() => toast.classList.add('show'), 100);
            
            // 自动隐藏
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // 禁用/启用生成按钮
        function setGenerateButtonState(disabled) {
            const btn = document.getElementById('generateBtn');
            if (btn) {
                btn.disabled = disabled;
                if (disabled) {
                    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 生成中...';
                } else {
                    btn.innerHTML = '<i class="fas fa-magic"></i> 生成论文';
                }
            }
        }
    </script>
</body>
</html>