<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
            background-color: #f5f7fa;
            min-height: 100vh;
            color: #2c3e50;
        }

        .back-home {
            position: fixed;
            top: 20px;
            left: 20px;
            color: #7f8c8d;
            text-decoration: none;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 6px;
            z-index: 1000;
            transition: color 0.3s ease;
        }

        .back-home:hover {
            color: #2c3e50;
            text-decoration: none;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 60px 20px 40px;
        }

        .profile-header {
            background: white;
            border-radius: 12px;
            padding: 40px;
            margin-bottom: 32px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            text-align: center;
        }

        .profile-avatar {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 40px;
            color: white;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
        }

        .profile-name {
            font-size: 28px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 12px;
        }

        .profile-balance {
            font-size: 20px;
            color: #10b981;
            font-weight: 600;
            background: #ecfdf5;
            padding: 8px 20px;
            border-radius: 20px;
            display: inline-block;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 32px 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 24px;
            color: white;
        }

        .stat-icon.recharge {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
        }

        .stat-icon.consumption {
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
        }

        .stat-icon.invite {
            background: linear-gradient(135deg, #feca57, #ff9ff3);
        }

        .stat-value {
            font-size: 28px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 16px;
            color: #6b7280;
            font-weight: 500;
        }

        .action-section {
            background: white;
            border-radius: 12px;
            padding: 40px;
            margin-bottom: 32px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 32px;
            text-align: center;
        }

        .recharge-form {
            max-width: 400px;
            margin: 0 auto;
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-label {
            display: block;
            color: #374151;
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .form-control {
            width: 100%;
            padding: 16px 20px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
        }

        .btn-recharge {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 16px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-recharge:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .invite-section {
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .invite-code-display {
            background: #f8fafc;
            border: 2px dashed #cbd5e1;
            border-radius: 12px;
            padding: 24px;
            text-align: center;
            margin-bottom: 20px;
        }

        .invite-code {
            font-size: 32px;
            font-weight: 700;
            color: #667eea;
            letter-spacing: 4px;
            font-family: 'Courier New', monospace;
        }

        .btn-copy {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            margin-top: 12px;
            transition: background-color 0.3s ease;
        }

        .btn-copy:hover {
            background: #5a67d8;
        }

        .invite-info {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 20px;
            font-size: 15px;
            color: #1e40af;
            line-height: 1.6;
        }

        .alert {
            padding: 16px 20px;
            border-radius: 8px;
            margin-bottom: 24px;
            font-size: 15px;
        }

        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .alert-danger {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }
    </style>
</head>
<body>
    <a href="/" class="back-home">
        <i class="fas fa-arrow-left"></i> 返回首页
    </a>

    <div class="container">
        <!-- 个人信息头部 -->
        <div class="profile-header">
            <div class="profile-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="profile-name">{{ user_info.username }}</div>
            <div class="profile-balance">账户余额: ¥{{ "%.2f"|format(user_info.balance) }}</div>
        </div>

        <!-- 统计数据 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon recharge">
                    <i class="fas fa-plus-circle"></i>
                </div>
                <div class="stat-value">¥{{ "%.2f"|format(user_info.total_recharge) }}</div>
                <div class="stat-label">累计充值</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon consumption">
                    <i class="fas fa-minus-circle"></i>
                </div>
                <div class="stat-value">¥{{ "%.2f"|format(user_info.total_consumption) }}</div>
                <div class="stat-label">累计消费</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon invite">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-value">{{ user_info.invite_count }}</div>
                <div class="stat-label">邀请人数</div>
            </div>
        </div>

        <!-- 充值功能 -->
        <div class="action-section">
            <h2 class="section-title">
                <i class="fas fa-credit-card"></i> 账户充值
            </h2>

            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.category }}">
                        {{ message.message }}
                    </div>
                {% endfor %}
            {% endif %}

            <form class="recharge-form" method="POST" action="/api/recharge">
                <div class="form-group">
                    <label class="form-label" for="amount">充值金额</label>
                    <input type="number"
                           class="form-control"
                           id="amount"
                           name="amount"
                           min="1"
                           step="0.01"
                           placeholder="请输入充值金额"
                           required>
                </div>
                <button type="submit" class="btn-recharge">
                    <i class="fas fa-plus"></i> 立即充值
                </button>
            </form>
        </div>

        <!-- 邀请功能 -->
        <div class="invite-section">
            <h2 class="section-title">
                <i class="fas fa-share-alt"></i> 邀请好友
            </h2>

            <div class="invite-code-display">
                <div class="invite-code">{{ user_info.invite_code }}</div>
                <button class="btn-copy" onclick="copyInviteCode()">
                    <i class="fas fa-copy"></i> 复制邀请码
                </button>
            </div>

            <div class="invite-info">
                <strong>邀请奖励说明：</strong><br>
                • 成功邀请一位新用户注册，您将获得 <strong>5元</strong> 奖励<br>
                • 被邀请用户首次充值，您将获得其充值金额 <strong>10%</strong> 的奖励<br>
                • 奖励将自动发放到您的账户余额中
            </div>
        </div>
    </div>

    <script>
        function copyInviteCode() {
            const inviteCode = '{{ user_info.invite_code }}';
            navigator.clipboard.writeText(inviteCode).then(function() {
                alert('邀请码已复制到剪贴板！');
            }, function(err) {
                console.error('复制失败: ', err);
                // 备用方案
                const textArea = document.createElement('textarea');
                textArea.value = inviteCode;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('邀请码已复制到剪贴板！');
            });
        }
    </script>
</body>
</html>
