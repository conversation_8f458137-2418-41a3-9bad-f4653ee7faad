"""
用户管理模块
包含用户注册、登录、额度管理、邀请码等功能
"""

import hashlib
import secrets
import string
import random
import json
from datetime import datetime, timedelta
from functools import wraps
from flask import session, request, jsonify, redirect, url_for
import pymysql
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UserManager:
    def __init__(self, db_config):
        """初始化用户管理器"""
        self.db_config = db_config
        
    def get_db_connection(self):
        """获取数据库连接"""
        # 确保使用utf8mb4字符集处理中文字符
        connection_params = {
            'host': self.db_config['host'],
            'user': self.db_config['user'],
            'password': self.db_config['password'],
            'database': self.db_config['database'],
            'charset': 'utf8mb4',
            'use_unicode': True,
            'autocommit': False,
            'cursorclass': pymysql.cursors.DictCursor
        }
        
        return pymysql.connect(**connection_params)
    
    def hash_password(self, password):
        """密码哈希"""
        return hashlib.sha256(password.encode('utf-8')).hexdigest()
    
    def generate_invite_code(self):
        """生成邀请码"""
        while True:
            # 生成格式：2个大写字母 + 6位数字
            letters = ''.join(random.choices(string.ascii_uppercase, k=2))
            numbers = ''.join(random.choices(string.digits, k=6))
            code = letters + numbers
            
            # 检查是否已存在
            conn = None
            try:
                conn = self.get_db_connection()
                with conn.cursor() as cursor:
                    cursor.execute("SELECT id FROM users WHERE invite_code = %s", (code,))
                    if not cursor.fetchone():
                        return code
            except Exception as e:
                logger.error(f"生成邀请码失败: {e}")
                return None
            finally:
                if conn:
                    conn.close()
    
    def register_user(self, username, password, invite_code=None):
        """用户注册"""
        conn = None
        try:
            conn = self.get_db_connection()
            with conn.cursor() as cursor:
                # 检查用户名是否已存在
                cursor.execute("SELECT id FROM users WHERE username = %s", (username,))
                if cursor.fetchone():
                    return {'success': False, 'message': '用户名已存在'}
                
                # 验证邀请码（如果提供）
                inviter_id = None
                if invite_code:
                    cursor.execute("SELECT id FROM users WHERE invite_code = %s", (invite_code,))
                    inviter = cursor.fetchone()
                    if not inviter:
                        return {'success': False, 'message': '邀请码无效'}
                    inviter_id = inviter['id']
                
                # 生成新用户的邀请码
                user_invite_code = self.generate_invite_code()
                
                # 创建用户
                password_hash = self.hash_password(password)
                new_user_bonus = self.get_system_config('new_user_bonus', 10.00)
                
                cursor.execute("""
                    INSERT INTO users (username, password_hash, balance, invite_code, invited_by)
                    VALUES (%s, %s, %s, %s, %s)
                """, (username, password_hash, new_user_bonus, user_invite_code, invite_code))
                
                user_id = cursor.lastrowid
                
                # 如果有邀请人，给邀请人奖励
                if inviter_id:
                    invite_reward = self.get_system_config('invite_reward', 5.00)
                    
                    # 更新邀请人余额
                    cursor.execute("""
                        UPDATE users SET 
                            balance = balance + %s,
                            invite_earnings = invite_earnings + %s
                        WHERE id = %s
                    """, (invite_reward, invite_reward, inviter_id))
                    
                    # 记录邀请记录
                    cursor.execute("""
                        INSERT INTO invite_records (inviter_id, invitee_id, invite_code, reward_amount)
                        VALUES (%s, %s, %s, %s)
                    """, (inviter_id, user_id, invite_code, invite_reward))
                
                conn.commit()
                
                return {
                    'success': True, 
                    'message': '注册成功',
                    'user_id': user_id,
                    'invite_code': user_invite_code,
                    'bonus': new_user_bonus
                }
                
        except Exception as e:
            logger.error(f"用户注册失败: {e}")
            return {'success': False, 'message': '注册失败，请稍后重试'}
        finally:
            if conn:
                conn.close()
    
    def login_user(self, username, password):
        """用户登录"""
        conn = None
        try:
            conn = self.get_db_connection()
            with conn.cursor() as cursor:
                password_hash = self.hash_password(password)
                cursor.execute("""
                    SELECT id, username, balance, invite_code, status, role
                    FROM users 
                    WHERE username = %s AND password_hash = %s
                """, (username, password_hash))
                
                user = cursor.fetchone()
                if not user:
                    return {'success': False, 'message': '用户名或密码错误'}
                
                if user['status'] != 1:
                    return {'success': False, 'message': '账户已被禁用'}
                
                # 更新最后登录时间
                cursor.execute("""
                    UPDATE users SET last_login_at = NOW() WHERE id = %s
                """, (user['id'],))
                conn.commit()
                
                return {
                    'success': True,
                    'message': '登录成功',
                    'user': user
                }
                
        except Exception as e:
            logger.error(f"用户登录失败: {e}")
            return {'success': False, 'message': '登录失败，请稍后重试'}
        finally:
            if conn:
                conn.close()
    
    def get_user_info(self, user_id):
        """获取用户信息"""
        conn = None
        try:
            conn = self.get_db_connection()
            with conn.cursor() as cursor:
                cursor.execute("""
                    SELECT id, username, balance, invite_code, total_recharge, 
                           total_consumption, invite_earnings, created_at, last_login_at, role
                    FROM users WHERE id = %s
                """, (user_id,))
                
                user = cursor.fetchone()
                if user:
                    # 获取邀请统计
                    cursor.execute("""
                        SELECT COUNT(*) as invite_count, COALESCE(SUM(reward_amount), 0) as total_rewards
                        FROM invite_records WHERE inviter_id = %s
                    """, (user_id,))
                    invite_stats = cursor.fetchone()
                    
                    user.update(invite_stats)
                
                return user
                
        except Exception as e:
            logger.error(f"获取用户信息失败: {e}")
            return None
        finally:
            if conn:
                conn.close()
    
    def get_system_config(self, key, default_value):
        """获取系统配置"""
        # 简化版本，直接返回默认值
        config_map = {
            'new_user_bonus': 10.00,
            'invite_reward': 5.00,
            'sql_to_er_cost': 1.00,
            'doc_generation_cost': 2.00,
            'ai_test_case_cost': 3.00,
            'thesis_defense_cost': 5.00,
            'paper_structure_cost': 4.00
        }
        return config_map.get(key, default_value)
    
    def is_admin(self, user_id):
        """检查用户是否为管理员"""
        conn = None
        try:
            conn = self.get_db_connection()
            with conn.cursor() as cursor:
                cursor.execute("SELECT role FROM users WHERE id = %s", (user_id,))
                user = cursor.fetchone()
                return user and user['role'] == 'admin'
        except Exception as e:
            logger.error(f"检查管理员权限失败: {e}")
            return False
        finally:
            if conn:
                conn.close()

    def consume_balance(self, user_id, amount, service_type, description=""):
        """扣除用户余额"""
        conn = None
        try:
            conn = self.get_db_connection()
            with conn.cursor() as cursor:
                # 检查余额是否足够
                cursor.execute("SELECT balance FROM users WHERE id = %s", (user_id,))
                user = cursor.fetchone()
                if not user or user['balance'] < amount:
                    return {'success': False, 'message': '余额不足'}

                # 扣除余额
                cursor.execute("""
                    UPDATE users SET
                        balance = balance - %s,
                        total_consumption = total_consumption + %s
                    WHERE id = %s
                """, (amount, amount, user_id))

                # 记录消费记录
                cursor.execute("""
                    INSERT INTO consumption_records (user_id, amount, service_type, description)
                    VALUES (%s, %s, %s, %s)
                """, (user_id, amount, service_type, description))

                conn.commit()
                return {'success': True, 'message': '扣费成功'}

        except Exception as e:
            logger.error(f"扣除余额失败: {e}")
            return {'success': False, 'message': '扣费失败'}
        finally:
            if conn:
                conn.close()

    def get_consumption_records(self, user_id, limit=50):
        """获取用户消费记录"""
        conn = None
        try:
            conn = self.get_db_connection()
            with conn.cursor() as cursor:
                cursor.execute("""
                    SELECT amount, service_type, description, created_at
                    FROM consumption_records
                    WHERE user_id = %s
                    ORDER BY created_at DESC
                    LIMIT %s
                """, (user_id, limit))

                return cursor.fetchall()

        except Exception as e:
            logger.error(f"获取消费记录失败: {e}")
            return []
        finally:
            if conn:
                conn.close()

    def save_defense_question_history(self, user_id, thesis_title, research_field, questions, generation_mode):
        """保存答辩问题历史记录"""
        conn = None
        try:
            conn = self.get_db_connection()
            with conn.cursor() as cursor:
                cursor.execute("""
                    INSERT INTO defense_question_history
                    (user_id, thesis_title, research_field, questions_json, generation_mode)
                    VALUES (%s, %s, %s, %s, %s)
                """, (user_id, thesis_title, research_field, str(questions), generation_mode))

                conn.commit()
                return cursor.lastrowid

        except Exception as e:
            logger.error(f"保存答辩问题历史失败: {e}")
            return None
        finally:
            if conn:
                conn.close()

    def get_defense_question_history(self, user_id, page=1, per_page=20):
        """获取答辩问题历史记录"""
        conn = None
        try:
            conn = self.get_db_connection()
            with conn.cursor() as cursor:
                offset = (page - 1) * per_page
                cursor.execute("""
                    SELECT id, thesis_title, research_field, generation_mode, created_at
                    FROM defense_question_history
                    WHERE user_id = %s
                    ORDER BY created_at DESC
                    LIMIT %s OFFSET %s
                """, (user_id, per_page, offset))

                records = cursor.fetchall()

                # 获取总数
                cursor.execute("""
                    SELECT COUNT(*) as total
                    FROM defense_question_history
                    WHERE user_id = %s
                """, (user_id,))
                total = cursor.fetchone()['total']

                return {
                    'records': records,
                    'total': total,
                    'page': page,
                    'per_page': per_page,
                    'total_pages': (total + per_page - 1) // per_page
                }

        except Exception as e:
            logger.error(f"获取答辩问题历史失败: {e}")
            return {'records': [], 'total': 0, 'page': 1, 'per_page': per_page, 'total_pages': 0}
        finally:
            if conn:
                conn.close()

    def get_defense_question_detail(self, user_id, record_id):
        """获取答辩问题详情"""
        conn = None
        try:
            conn = self.get_db_connection()
            with conn.cursor() as cursor:
                cursor.execute("""
                    SELECT * FROM defense_question_history
                    WHERE id = %s AND user_id = %s
                """, (record_id, user_id))

                return cursor.fetchone()

        except Exception as e:
            logger.error(f"获取答辩问题详情失败: {e}")
            return None
        finally:
            if conn:
                conn.close()

    def delete_defense_question_history(self, user_id, record_id):
        """删除答辩问题历史记录"""
        conn = None
        try:
            conn = self.get_db_connection()
            with conn.cursor() as cursor:
                cursor.execute("""
                    DELETE FROM defense_question_history
                    WHERE id = %s AND user_id = %s
                """, (record_id, user_id))

                conn.commit()
                return cursor.rowcount > 0

        except Exception as e:
            logger.error(f"删除答辩问题历史失败: {e}")
            return False
        finally:
            if conn:
                conn.close()

    def clear_defense_question_history(self, user_id):
        """清空用户所有答辩问题历史记录"""
        conn = None
        try:
            conn = self.get_db_connection()
            with conn.cursor() as cursor:
                cursor.execute("""
                    DELETE FROM defense_question_history
                    WHERE user_id = %s
                """, (user_id,))

                count = cursor.rowcount
                conn.commit()
                return count

        except Exception as e:
            logger.error(f"清空答辩问题历史失败: {e}")
            return 0
        finally:
            if conn:
                conn.close()

    def save_paper(self, user_id, title, field, paper_type, target_words, abstract, keywords, content, html_content):
        """保存论文到数据库"""
        conn = None
        try:
            conn = self.get_db_connection()
            
            # 首先确保papers表存在
            with conn.cursor() as cursor:
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS papers (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        user_id INT,
                        title VARCHAR(500) NOT NULL,
                        field VARCHAR(200),
                        paper_type VARCHAR(100),
                        target_words INT,
                        abstract TEXT,
                        keywords TEXT,
                        content JSON,
                        html_content LONGTEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        status VARCHAR(50) DEFAULT 'completed',
                        INDEX idx_user_id (user_id),
                        INDEX idx_created_at (created_at)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                """)
                
                # 插入论文数据
                cursor.execute("""
                    INSERT INTO papers (
                        user_id, title, field, paper_type, target_words, 
                        abstract, keywords, content, html_content, status
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    user_id, title, field, paper_type, target_words,
                    abstract, keywords, 
                    json.dumps(content, ensure_ascii=False) if content else None,
                    html_content, 'completed'
                ))
                
                paper_id = cursor.lastrowid
                conn.commit()
                
                logger.info(f"论文保存成功 - ID: {paper_id}, 用户: {user_id}, 标题: {title}")
                return paper_id
                
        except Exception as e:
            logger.error(f"保存论文失败: {e}")
            if conn:
                conn.rollback()
            return None
        finally:
            if conn:
                conn.close()

    def get_user_papers(self, user_id, page=1, per_page=20):
        """获取用户的论文列表"""
        conn = None
        try:
            conn = self.get_db_connection()
            
            with conn.cursor() as cursor:
                # 获取总数
                cursor.execute("SELECT COUNT(*) as total FROM papers WHERE user_id = %s", (user_id,))
                total = cursor.fetchone()['total']
                
                # 获取分页数据
                offset = (page - 1) * per_page
                cursor.execute("""
                    SELECT id, title, field, paper_type, target_words, 
                           abstract, keywords, created_at, updated_at, status,
                           CHAR_LENGTH(html_content) as content_length
                    FROM papers 
                    WHERE user_id = %s 
                    ORDER BY created_at DESC 
                    LIMIT %s OFFSET %s
                """, (user_id, per_page, offset))
                
                papers = cursor.fetchall()
                
                return {
                    'papers': papers,
                    'total': total,
                    'page': page,
                    'per_page': per_page,
                    'pages': (total + per_page - 1) // per_page
                }
                
        except Exception as e:
            logger.error(f"获取用户论文列表失败: {e}")
            return None
        finally:
            if conn:
                conn.close()

    def get_paper_detail(self, user_id, paper_id):
        """获取论文详细内容"""
        conn = None
        try:
            conn = self.get_db_connection()
            
            with conn.cursor() as cursor:
                cursor.execute("""
                    SELECT * FROM papers 
                    WHERE id = %s AND (user_id = %s OR user_id IS NULL)
                """, (paper_id, user_id))
                
                paper = cursor.fetchone()
                
                if paper and paper['content']:
                    try:
                        paper['content'] = json.loads(paper['content'])
                    except:
                        paper['content'] = {}
                
                return paper
                
        except Exception as e:
            logger.error(f"获取论文详情失败: {e}")
            return None
        finally:
            if conn:
                conn.close()



# 装饰器：检查用户登录状态
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            if request.is_json:
                return jsonify({'success': False, 'message': '请先登录'}), 401
            else:
                return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function
