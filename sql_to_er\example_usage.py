#!/usr/bin/env python3
"""
Example usage of SQL to ER converter as a Python module
"""
from src import parse_sql, build_er_model, render_er_diagram

# Define SQL directly in code
sql = """
CREATE TABLE Author (
    author_id INT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE
);

CREATE TABLE Book (
    book_id INT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    isbn VARCHAR(20) UNIQUE,
    author_id INT,
    price DECIMAL(8, 2),
    FOREIGN KEY (author_id) REFERENCES Author(author_id)
);

CREATE TABLE Reader (
    reader_id INT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE
);

CREATE TABLE Review (
    review_id INT PRIMARY KEY,
    book_id INT,
    reader_id INT,
    rating INT CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    FOREIG<PERSON> KEY (book_id) REFERENCES Book(book_id),
    FOREIG<PERSON> KEY (reader_id) REFERENCES Reader(reader_id)
);
"""

# Parse SQL
print("Parsing SQL...")
tables, error = parse_sql(sql)

if error:
    print(f"Error: {error}")
    exit(1)

# Show parsed information
print("\nParsed tables:")
for table_name, table_info in tables.items():
    print(f"\n{table_name}:")
    print(f"  Primary Keys: {', '.join(table_info['primary_keys'])}")
    print(f"  Columns: {', '.join([col['name'] for col in table_info['columns']])}")
    if table_info['foreign_keys']:
        print(f"  Foreign Keys:")
        for fk in table_info['foreign_keys']:
            print(f"    - {fk['column']} -> {fk['ref']['table']}.{fk['ref']['column']}")

# Build ER model
entities, relationships = build_er_model(tables)

# Render diagram
print("\nGenerating ER diagram...")
output_path = render_er_diagram(entities, relationships, "library_example", view=False)
print(f"ER diagram saved to: {output_path}")